{"bill": {"SalesTermRef": {"value": "6"}, "Line": [{"DetailType": "ItemBasedExpenseLineDetail", "Description": "MAXIPULL EMBOSSED 2PLY 1X6 PURE P650 SC", "Amount": 456, "ItemBasedExpenseLineDetail": {"ItemRef": {"value": "22"}, "UnitPrice": 38, "Qty": 12, "TaxCodeRef": {"value": "5"}}}, {"DetailType": "ItemBasedExpenseLineDetail", "Description": "LD GARBAGE BAG RECYCLED 110X130CM BLACK 20KG", "Amount": 95, "ItemBasedExpenseLineDetail": {"ItemRef": {"value": "26"}, "UnitPrice": 95, "Qty": 1, "TaxCodeRef": {"value": "5"}}}], "PrivateNote": "Tax Invoice Number: DICSI25030927 Generated by <PERSON><PERSON><PERSON><PERSON> at Tue Jul 01 2025 00:09:20 GMT+0300 (GMT+03:00)", "VendorRef": {"value": "39"}, "TxnDate": "2025-03-06", "DueDate": "2025-04-30", "GlobalTaxCalculation": "TaxExcluded"}, "taxes": [{"name": "5 %", "id": "14", "rate": 0}, {"name": "EX Exempt", "id": "4", "rate": 0}, {"name": "IG Intra GCC", "id": "9", "rate": 0}, {"name": "RC Reverse Charge", "id": "10", "rate": 0}, {"name": "SR Standard Rated", "id": "5", "rate": 5}, {"name": "SR Standard Rated (AJM)", "id": "7", "rate": 0}, {"name": "SR Standard Rated (AUH)", "id": "6", "rate": 0}, {"name": "SR Standard Rated (DXB)", "id": "12", "rate": 0}, {"name": "SR Standard Rated (FUJ)", "id": "3", "rate": 0}, {"name": "SR Standard Rated (RAK)", "id": "8", "rate": 0}, {"name": "SR Standard Rated (SHJ)", "id": "13", "rate": 0}, {"name": "SR Standard Rated (UAQ)", "id": "11", "rate": 0}, {"name": "ZR Zero Rated", "id": "2", "rate": 0}], "vendors": [{"DisplayName": "Etisalat", "Id": "16"}, {"DisplayName": "Excelur Foodstuff Trading LLC", "Id": "213"}, {"DisplayName": "FLABS INTERIOR DECORATION LLC", "Id": "209"}, {"DisplayName": "Trans Vision Sea Shipping Lines Agents LLC", "Id": "89"}, {"DisplayName": "AL GHAZAL TRANSPORT", "Id": "461"}, {"DisplayName": "ALBA Foodstuff Trading LLC", "Id": "153"}, {"DisplayName": "Aalmir Plastic Industries L.LC", "Id": "14"}, {"DisplayName": "Agrano Gmbh & Co. KG", "Id": "129"}, {"DisplayName": "Al Diyafa Hotel & Catering Supplies", "Id": "224"}, {"DisplayName": "Al Majal Company L.L.C", "Id": "22"}, {"DisplayName": "Al Makhzumi Security & Safety L.L.C", "Id": "15"}, {"DisplayName": "Al Nawras Automatic Laundry", "Id": "46"}, {"DisplayName": "Al Qaswa Aluminium & Glass Tr", "Id": "17"}, {"DisplayName": "Al Sharqi Shipping CO LLC", "Id": "482"}, {"DisplayName": "Al-ITTIHAD Al WATANI", "Id": "459"}, {"DisplayName": "<PERSON>", "Id": "154"}, {"DisplayName": "Amber Industries", "Id": "478"}, {"DisplayName": "Aramtec", "Id": "449"}, {"DisplayName": "Asonic Logistics Middle East LLC", "Id": "118"}, {"DisplayName": "Assured Food Safety by Specificoco and Co", "Id": "55"}, {"DisplayName": "AtoZ World", "Id": "244"}, {"DisplayName": "4 Corner General Trading LLC", "Id": "93"}, {"DisplayName": "AAPT Quality and Food Safety Consultancy", "Id": "235"}, {"DisplayName": "Belhasa Car Rental LLC", "Id": "674"}, {"DisplayName": "BioTek Solutions LLC", "Id": "19"}, {"DisplayName": "<PERSON>ecker Public Health Dubai", "Id": "35"}, {"DisplayName": "<PERSON><PERSON><PERSON>", "Id": "84"}, {"DisplayName": "Bureau Veritas Branch Dubai", "Id": "236"}, {"DisplayName": "Cactus Design", "Id": "8"}, {"DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "Id": "64"}, {"DisplayName": "Chef Middle East", "Id": "163"}, {"DisplayName": "Chicago Metallic Bakeware", "Id": "119"}, {"DisplayName": "Classic Fine Foodsuff Trading LLC", "Id": "432"}, {"DisplayName": "Code Paradise Visuals", "Id": "324"}, {"DisplayName": "Commercial license", "Id": "285"}, {"DisplayName": "Country Hill International", "Id": "25"}, {"DisplayName": "Cypher Roastary LLC", "Id": "334"}, {"DisplayName": "Danat Alain IT Infrastructure LLC", "Id": "323"}, {"DisplayName": "<PERSON>", "Id": "37"}, {"DisplayName": "Department Of Economic Development", "Id": "2"}, {"DisplayName": "<PERSON><PERSON>", "Id": "38"}, {"DisplayName": "Dhofar Global Trading Co. L.L.C.", "Id": "39"}, {"DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "Id": "202"}, {"DisplayName": "Dubai Insurance", "Id": "319"}, {"DisplayName": "Dubai Municipality", "Id": "109"}, {"DisplayName": "EMF EMIRATES LLC", "Id": "160"}, {"DisplayName": "Emirates Companies House LLC", "Id": "1"}, {"DisplayName": "Emirates Snack Foods L.L.C", "Id": "24"}, {"DisplayName": "FSL FOODS FZE", "Id": "417"}, {"DisplayName": "Falconpack", "Id": "23"}, {"DisplayName": "FedEx", "Id": "112"}, {"DisplayName": "Fine Hygienic Paper FZE", "Id": "166"}, {"DisplayName": "Fresh Express L.L.C", "Id": "49"}, {"DisplayName": "Fujairah Plastic Factories", "Id": "126"}, {"DisplayName": "G4tec Electro Mechanical LLC", "Id": "151"}, {"DisplayName": "GOCACTUS Inc", "Id": "117"}, {"DisplayName": "Gas Stations", "Id": "405"}, {"DisplayName": "General Supplier", "Id": "642"}, {"DisplayName": "Google Ads", "Id": "313"}, {"DisplayName": "Google Cloud - GSuite", "Id": "56"}, {"DisplayName": "Gtec Technical Services LLC", "Id": "210"}, {"DisplayName": "Gulf International", "Id": "63"}, {"DisplayName": "Hawk Freight Services FZE", "Id": "215"}, {"DisplayName": "Heidi Chef Solutions L.L.C", "Id": "21"}, {"DisplayName": "High Five Foodstuff Trading LLC", "Id": "423"}, {"DisplayName": "Hot Pack Packaging L.L.C", "Id": "29"}, {"DisplayName": "Indigo Advertising LLC", "Id": "121"}, {"DisplayName": "Inspectorate International Limited", "Id": "234"}, {"DisplayName": "J  M Foods LLC", "Id": "75"}, {"DisplayName": "Jaleel Foodservices LLC", "Id": "31"}, {"DisplayName": "Jenin.ae", "Id": "463"}, {"DisplayName": "Jet Fire & Safety LLC", "Id": "268"}, {"DisplayName": "Kangaroo", "Id": "293"}, {"DisplayName": "Klaviyo Incorporation Software Boston USA", "Id": "335"}, {"DisplayName": "Lab Eight", "Id": "320"}, {"DisplayName": "Lakeland", "Id": "6"}, {"DisplayName": "M.H.Enterprises L.L.C", "Id": "43"}, {"DisplayName": "MIWE Middle East FZE", "Id": "110"}, {"DisplayName": "Marino Felice", "Id": "176"}, {"DisplayName": "Master <PERSON>", "Id": "444"}, {"DisplayName": "Matfer Bourgeat Gulf", "Id": "183"}, {"DisplayName": "Matrix Media Solutions (P) Ltd", "Id": "273"}, {"DisplayName": "Mega Impex Software House LLC", "Id": "98"}, {"DisplayName": "Ministry Of Human Resources & Emiratisation", "Id": "13"}, {"DisplayName": "<PERSON><PERSON><PERSON>", "Id": "470"}, {"DisplayName": "<PERSON><PERSON>", "Id": "152"}, {"DisplayName": "<PERSON><PERSON><PERSON>", "Id": "295"}, {"DisplayName": "Ocean Fair International Group FZE", "Id": "113"}, {"DisplayName": "Oman Insurance Company (PSC)", "Id": "161"}, {"DisplayName": "Onfleet", "Id": "643"}, {"DisplayName": "Organic Planet Foods Trading LLC", "Id": "279"}, {"DisplayName": "Orient Insurance PJSC", "Id": "208"}, {"DisplayName": "<PERSON><PERSON>", "Id": "269"}, {"DisplayName": "Pemo", "Id": "453"}, {"DisplayName": "The Pizza  Guys Restaurant LLC", "Id": "60"}, {"DisplayName": "QASHIO FOR COMPUTER SYSTEMS & CEST LLC", "Id": "326"}, {"DisplayName": "Quickbooks", "Id": "3"}, {"DisplayName": "Right Media Advertising & Trading FZE – LLC", "Id": "300"}, {"DisplayName": "SGS Gulf Limited", "Id": "216"}, {"DisplayName": "<PERSON><PERSON> Falasi", "Id": "167"}, {"DisplayName": "Sasso & Co.", "Id": "116"}, {"DisplayName": "Sawhney Foodstuff Trading Co LLC", "Id": "175"}, {"DisplayName": "<PERSON><PERSON>", "Id": "5"}, {"DisplayName": "Siha&Afia", "Id": "452"}, {"DisplayName": "Smartway Accounting & Tax Services", "Id": "227"}, {"DisplayName": "Sofia Refrigeration", "Id": "451"}, {"DisplayName": "Studio Foreign Design & Artwork Services", "Id": "281"}, {"DisplayName": "Super Market", "Id": "406"}, {"DisplayName": "T.CHOITHRAM & SONS LLC", "Id": "241"}, {"DisplayName": "TCL TECHNICAL CHEMICAL LABORATORIES", "Id": "569"}, {"DisplayName": "TFL Technical Services LLC", "Id": "10"}, {"DisplayName": "TSSC Food & Beverage Trading", "Id": "85"}, {"DisplayName": "TSSC Kitchen & Laundry Equipment Trading LLC", "Id": "86"}, {"DisplayName": "Tech Steel FZE", "Id": "240"}, {"DisplayName": "Technical Supplies & Services Co. LLC", "Id": "12"}, {"DisplayName": "Tetra Axis LLC", "Id": "36"}, {"DisplayName": "Transmed Overseas Incorporates", "Id": "256"}, {"DisplayName": "Trofima Foodstuff Trading L.L.C", "Id": "26"}, {"DisplayName": "Union Insurance Company", "Id": "27"}, {"DisplayName": "United Car Rentals", "Id": "67"}, {"DisplayName": "Us Creative", "Id": "221"}, {"DisplayName": "Vanilla Beans Store", "Id": "343"}, {"DisplayName": "W.J. Towell LLC", "Id": "431"}, {"DisplayName": "Wessam  MHMB", "Id": "292"}, {"DisplayName": "Youssry & Co Accounting & Consultiancy", "Id": "469"}, {"DisplayName": "ZECORP KITCHEN EQUIPMENT TRADING L.L.C", "Id": "672"}], "items": [{"Name": "CINNAMON CHERRY BRIOCHE", "Id": "296"}, {"Name": "<PERSON>in Palestinian Extra Virgin Olive Oil - 500ml", "Id": "334"}, {"Name": "<PERSON><PERSON>bali Olives - 500g", "Id": "335"}, {"Name": "<PERSON><PERSON> - 250g", "Id": "336"}, {"Name": "Olive and Zattar So<PERSON>ugh", "Id": "252"}, {"Name": "Organic Baguette Tradition", "Id": "255"}, {"Name": "Organic Country Batard - 650g", "Id": "127"}, {"Name": "Organic Country Sandwich Loaf - 650g", "Id": "126"}, {"Name": "ORGANIC FRENCH COUNTRY BREAD - TOURTE DE MEULE", "Id": "256"}, {"Name": "ORGANIC FRENCH COUNTRY BREAD - TOURTE DE MEULE 900G", "Id": "320"}, {"Name": "Organic French Rye", "Id": "201"}, {"Name": "Organic French Rye 1000 grams", "Id": "361"}, {"Name": "ORGANIC FRENCH WHOLE WHEAT BREAD - TOURTE DE MEULE", "Id": "313"}, {"Name": "ADVERTISEMENT FEES YEARLY", "Id": "242"}, {"Name": "ADVERTISING FEES", "Id": "246"}, {"Name": "Bad Debts", "Id": "348"}, {"Name": "Blend", "Id": "294"}, {"Name": "Coffee- Morning Blend (<PERSON> Sticker on black bag)", "Id": "295"}, {"Name": "Bread", "Id": "41"}, {"Name": "(<PERSON><PERSON>) Nabali Olives - 1KG", "Id": "343"}, {"Name": "(<PERSON><PERSON>) Nabali Olives - 500g", "Id": "342"}, {"Name": "(<PERSON><PERSON>) Zaatar - 1KG", "Id": "344"}, {"Name": "(<PERSON><PERSON>) Zaatar - 250G", "Id": "345"}, {"Name": "Better Bread Organic White Sandwich Bread", "Id": "164"}, {"Name": "Better Bread Organic Whole Wheat Sandwich Bread", "Id": "165"}, {"Name": "Brioche Buns - R", "Id": "87"}, {"Name": "Chocolate Brioche", "Id": "170"}, {"Name": "Chocolate Chip Brioche - 600g", "Id": "95"}, {"Name": "Chocolate Chip Sourdough", "Id": "93"}, {"Name": "Chocolate Sourdough - 550 g", "Id": "172"}, {"Name": "Organic Multiseed 700g", "Id": "221"}, {"Name": "Organic Olive and Zaatar Bread 625G", "Id": "332"}, {"Name": "ORGANIC OVERNIGHT OATS SOURDOUGH", "Id": "349"}, {"Name": "ORGANIC SOURDOUGH ANCIENT GRAINS BATARD", "Id": "325"}, {"Name": "Organic Sourdough Country Batard 1000g", "Id": "220"}, {"Name": "Organic Sourdough Country Batard 1200g", "Id": "254"}, {"Name": "Organic Sourdough Country Batard 700g", "Id": "173"}, {"Name": "Organic Sourdough Country Batard 800g", "Id": "218"}, {"Name": "Organic Sourdough Country Bread W/Black Sesame", "Id": "70"}, {"Name": "Organic Sourdough Country Sandwich Loaf 700 grams", "Id": "97"}, {"Name": "Organic Whole Wheat Batard- 1000g", "Id": "362"}, {"Name": "Organic Whole Wheat Batard- 650g", "Id": "225"}, {"Name": "Organic Whole Wheat Boule - 650g", "Id": "128"}, {"Name": "Organic Whole Wheat Sandwich Loaf - 650g", "Id": "125"}, {"Name": "Pairing - Organic WW & Moliterno Al Tartufo", "Id": "210"}, {"Name": "Palestinian Olive and Z<PERSON>'atar Sourdough Batard", "Id": "333"}, {"Name": "Sourdough Potato Buns - 52 gms", "Id": "96"}, {"Name": "Sourdough Potato Loaf", "Id": "138"}, {"Name": "Traditional French Brioche - 300g", "Id": "316"}, {"Name": "Traditional French Brioche - 600g", "Id": "94"}, {"Name": "WALNUT RAISIN SOURDOUGH", "Id": "289"}, {"Name": "Bread Crumbs", "Id": "49"}, {"Name": "Italian Bread Crumbs", "Id": "51"}, {"Name": "Plain Bread Crumbs", "Id": "50"}, {"Name": "Whole Wheat Bread Crumbs", "Id": "52"}, {"Name": "Buns", "Id": "114"}, {"Name": "Artisan Brioche Buns", "Id": "272"}, {"Name": "Artisan Brioche Buns 120g", "Id": "365"}, {"Name": "Artisan Brioche Buns 30 Grams", "Id": "360"}, {"Name": "Chocolate Brioche Buns", "Id": "115"}, {"Name": "Chocolate", "Id": "35"}, {"Name": "Ecuador 40% Cacao, Milk Chocolate Caramelized", "Id": "36"}, {"Name": "Cleaning Chemicals", "Id": "3"}, {"Name": "BH20-5L All Purpose Cleaner & De-greaser 5 Litter", "Id": "4"}, {"Name": "BH30-2L Clean & Disinfect 2 Liters", "Id": "5"}, {"Name": "BH33P-2L Stainless Steel Polish RTU", "Id": "6"}, {"Name": "BH34-5L Pot Wash (Bactericidal) 5 Liter", "Id": "7"}, {"Name": "BH36-25L Auto Dishwasher Detergent 25 Liters", "Id": "9"}, {"Name": "BH37-25L Auto Dishwasher Rinse Aid 25 Liters", "Id": "10"}, {"Name": "BH39-2L Glass & Steel Cleaner Conc. 2 Liters", "Id": "11"}, {"Name": "BH41-5L Hand Soap (Bactericidal) 5 Litters", "Id": "8"}, {"Name": "BH42L-5 Liquid Hand Sanitizer 5 Liter", "Id": "13"}, {"Name": "BH53-5L Pine Disinfectant 5 Liters", "Id": "12"}, {"Name": "BHXTR10 Biosafe XTR (10L)", "Id": "14"}, {"Name": "Commission Income", "Id": "346"}, {"Name": "CUSTOMER ENGAGEMENT FEES", "Id": "249"}, {"Name": "Dairy", "Id": "33"}, {"Name": "Butter", "Id": "78"}, {"Name": "Cooking Cream", "Id": "111"}, {"Name": "Cow Milk <PERSON><PERSON><PERSON>", "Id": "34"}, {"Name": "Eggs White", "Id": "79"}, {"Name": "<PERSON><PERSON>", "Id": "130"}, {"Name": "FIORDILATTE J SHREDDED MOZZARELLA", "Id": "140"}, {"Name": "FRO Moliterno al tartufo", "Id": "217"}, {"Name": "Grana Padano Wheel", "Id": "55"}, {"Name": "Manchego Semicurad", "Id": "205"}, {"Name": "Milk Fresh", "Id": "80"}, {"Name": "Organic Active Dried Yeast -10 kg", "Id": "168"}, {"Name": "DISPLAY FEES", "Id": "245"}, {"Name": "Distribution Charges", "Id": "150"}, {"Name": "<PERSON><PERSON>", "Id": "47"}, {"Name": "Fresh Pizza Dough", "Id": "160"}, {"Name": "Fresh Pizza Dough - Cut", "Id": "180"}, {"Name": "Fresh Pizza Dough 100g", "Id": "263"}, {"Name": "Fresh Sourdough Pizza Napoletana Dough", "Id": "48"}, {"Name": "Drain Assembly", "Id": "193"}, {"Name": "<PERSON><PERSON>", "Id": "195"}, {"Name": "ECOMMERCE FEES", "Id": "250"}, {"Name": "Expenses", "Id": "154"}, {"Name": "Price Difference Nutritional Test", "Id": "188"}, {"Name": "Shipping & Clearance Exp", "Id": "155"}, {"Name": "Flour Croissant \"00\" Strong Pi<PERSON>oni", "Id": "200"}, {"Name": "Flours", "Id": "15"}, {"Name": "\"0\" Professional <PERSON><PERSON><PERSON>", "Id": "147"}, {"Name": "0 <PERSON><PERSON> 25 kg Di Grano", "Id": "135"}, {"Name": "AGUGIARO FARINA 00/ORO - GOLD FLOUR", "Id": "229"}, {"Name": "Agugiaro Farina 00/P.N Pizza Napoletana", "Id": "145"}, {"Name": "Agugiaro Farina 00/S Superior Flour", "Id": "144"}, {"Name": "Agugiaro Farina Integrale Fine", "Id": "324"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Id": "129"}, {"Name": "Caputo 0 Organic 25 kg", "Id": "185"}, {"Name": "Caputo Flour Blue 00 Pizzeria", "Id": "112"}, {"Name": "Caputo Flour Type 1", "Id": "189"}, {"Name": "Cotswold Organic Premium White", "Id": "110"}, {"Name": "Flour  Saccorosso Red 00 Reinforced", "Id": "297"}, {"Name": "Flour 00 R P", "Id": "31"}, {"Name": "Flour Focaccia Bread", "Id": "59"}, {"Name": "Flour Gruau T55 - Viron", "Id": "222"}, {"Name": "Flour Linea Organic 0 Wheat 25kg", "Id": "317"}, {"Name": "Flour Linea Organic Wholemeal 25kg", "Id": "318"}, {"Name": "Flour Nobilgrano Green", "Id": "30"}, {"Name": "Flour Organic \"0\" Strong Colombo 25kg", "Id": "177"}, {"Name": "Flour Organic \"0\" Weak Colombo 25kg", "Id": "176"}, {"Name": "Flour Pastry Brioche", "Id": "84"}, {"Name": "Flour Rice 1Kg", "Id": "73"}, {"Name": "Flour Se<PERSON><PERSON> Remilled", "Id": "109"}, {"Name": "Flour T45", "Id": "159"}, {"Name": "Flour T55", "Id": "108"}, {"Name": "Flour T65", "Id": "32"}, {"Name": "Flour Wheat T65(Mono pool Germany 25kg", "Id": "328"}, {"Name": "Flour Whole Wheat", "Id": "60"}, {"Name": "Flour Whole Wheat Fine Piantoni 25Kg", "Id": "326"}, {"Name": "<PERSON><PERSON><PERSON> 25 kg", "Id": "157"}, {"Name": "Molino Agugiaro Farina 0/S -0 Flour", "Id": "187"}, {"Name": "MOUL-BIE FLOUR GRUAU ROUGE FLOUR T45", "Id": "337"}, {"Name": "Organic  Wheat flour Type 1050 (Mono pool Kosher) 25kg", "Id": "319"}, {"Name": "Organic Khorasan Wheat Flour", "Id": "206"}, {"Name": "Organic Rye Flour 1150 25kg", "Id": "68"}, {"Name": "Organic Rye Flour Type 170", "Id": "118"}, {"Name": "Organic Sfarinato bio di grano duro 25 KG", "Id": "364"}, {"Name": "Organic Sfarinato Durum Wheat Flour 25kg", "Id": "359"}, {"Name": "Organic Spelt Flour T80", "Id": "207"}, {"Name": "Organic Wheat Flour T110", "Id": "290"}, {"Name": "Organic Wheat Flour T1300", "Id": "322"}, {"Name": "Organic Wheat Flour T150", "Id": "116"}, {"Name": "Organic Wheat Flour T55", "Id": "107"}, {"Name": "Organic Wheat Flour T650", "Id": "323"}, {"Name": "Organic Wheat Flour T80", "Id": "117"}, {"Name": "Organic Wheat Flour T812", "Id": "321"}, {"Name": "Organic Wheat Flour Type 65", "Id": "163"}, {"Name": "Organic Whole Wheat Flour", "Id": "16"}, {"Name": "Organic Wholemeal Wheat flour Mono pool Germany 25kg", "Id": "327"}, {"Name": "Pizza Blu Farina 00", "Id": "119"}, {"Name": "Pizza Rossa Farina 00", "Id": "124"}, {"Name": "Rye Flour T997", "Id": "331"}, {"Name": "Semola Durum Wheat Caputo 1 Kg", "Id": "235"}, {"Name": "SO 0 <PERSON><PERSON> 25 kg Di Grano", "Id": "146"}, {"Name": "Strong Organic Flour", "Id": "61"}, {"Name": "T55 Gruau", "Id": "191"}, {"Name": "Type 1 Grangusto Flexia - Rich Taste", "Id": "179"}, {"Name": "Wheat Flour T65", "Id": "162"}, {"Name": "Whole Grain Rye Flour", "Id": "17"}, {"Name": "Wholemeal Organic Colombo 25 kg", "Id": "178"}, {"Name": "Freight from Dubai to Bahrain", "Id": "106"}, {"Name": "GOVERNMENT PROMOTION FEE", "Id": "241"}, {"Name": "Hours", "Id": "2"}, {"Name": "INFLATION-FIXED INVESTMENT-2022", "Id": "276"}, {"Name": "Jams & Spreads", "Id": "299"}, {"Name": "<PERSON> Apricot Fruit Spread", "Id": "305"}, {"Name": "Shoe Cover", "Id": "152"}, {"Name": "<PERSON> Fig Fruit Spread", "Id": "302"}, {"Name": "<PERSON> Peach Apricot Fruit Spread", "Id": "304"}, {"Name": "<PERSON> Raspberry Fruit Spread", "Id": "301"}, {"Name": "<PERSON> Strawberry Fruit Spread", "Id": "300"}, {"Name": "<PERSON>ot Three Red Fruits Fruit Spread", "Id": "303"}, {"Name": "The Francis Miot Apricot Fruit Spread", "Id": "310"}, {"Name": "The Francis Miot Fig Fruit Spread", "Id": "311"}, {"Name": "The Francis Miot Orange Fruit Spread", "Id": "312"}, {"Name": "The Francis Miot Peach Apricot Fruit Spread", "Id": "309"}, {"Name": "The Francis Miot Raspberry Fruit Spread", "Id": "308"}, {"Name": "The Francis Miot Strawberry Fruit Spread", "Id": "306"}, {"Name": "The Francis Miot Three Red Fruits Fruit Spread", "Id": "307"}, {"Name": "Loss on <PERSON><PERSON><PERSON> Account", "Id": "223"}, {"Name": "<PERSON> Elianza dark couverture 55%", "Id": "208"}, {"Name": "<PERSON><PERSON>", "Id": "27"}, {"Name": "O - Ring", "Id": "194"}, {"Name": "Olive Oil", "Id": "28"}, {"Name": "(<PERSON><PERSON>) Extra Virgin Olive Oil - 500ml", "Id": "341"}, {"Name": "Eleia olive Oil", "Id": "29"}, {"Name": "Extra Virgin Olive Oil", "Id": "37"}, {"Name": "Opening Fee", "Id": "251"}, {"Name": "Organic Whole Wheat Boule", "Id": "216"}, {"Name": "Others", "Id": "56"}, {"Name": "Additional fee Deliveroo", "Id": "357"}, {"Name": "<PERSON>", "Id": "63"}, {"Name": "Beef Chorizo Spice Whole Piece", "Id": "58"}, {"Name": "Black Raisins", "Id": "288"}, {"Name": "BRAZIL AMENDOA", "Id": "354"}, {"Name": "Cherry Morello Frozen Lafruittere KG's", "Id": "291"}, {"Name": "Chocolate Dark 55% Chps 2x5 kg", "Id": "175"}, {"Name": "Cocoa Powder", "Id": "224"}, {"Name": "Dark Chocolate Nuggets", "Id": "136"}, {"Name": "Dark Couverture Chocolate", "Id": "183"}, {"Name": "Distribution Allowance", "Id": "153"}, {"Name": "EVO Casinetto Pet 5lt", "Id": "182"}, {"Name": "FlaxSeed", "Id": "270"}, {"Name": "Hairnet", "Id": "83"}, {"Name": "Iranian Walnut", "Id": "287"}, {"Name": "Jordan Green Olives W/Zaatar", "Id": "65"}, {"Name": "Less 15% Commission Adrish", "Id": "286"}, {"Name": "Less 20% Commission", "Id": "233"}, {"Name": "Less 20% Commission Rawteen", "Id": "363"}, {"Name": "Less 25% Commission Deliveroo", "Id": "356"}, {"Name": "Linseed", "Id": "215"}, {"Name": "Misc Items", "Id": "211"}, {"Name": "Organic Oats Flake", "Id": "350"}, {"Name": "PAPUA NEW GUINEA KASTAM", "Id": "355"}, {"Name": "Peeled Tomatoes Allegria 2.5 kg", "Id": "64"}, {"Name": "Peppadews Whole Fruit Hot", "Id": "156"}, {"Name": "Pizza Oven from Valoriani Italy", "Id": "105"}, {"Name": "Plastic Apron", "Id": "181"}, {"Name": "Potato", "Id": "89"}, {"Name": "<PERSON><PERSON><PERSON> Seeds", "Id": "214"}, {"Name": "RIGAMONTI SPICY SALAMI SPIANATA", "Id": "141"}, {"Name": "Salt of Trapani", "Id": "62"}, {"Name": "Sesame Seed", "Id": "212"}, {"Name": "Sesame Seeds Black", "Id": "81"}, {"Name": "Spicy Salami Spianata 1.8 kg", "Id": "69"}, {"Name": "Sugar", "Id": "85"}, {"Name": "Sunflower Seeds", "Id": "213"}, {"Name": "<PERSON><PERSON>", "Id": "102"}, {"Name": "THE MORNING BLEND", "Id": "293"}, {"Name": "THE MORNING BLEND - 100% ARABICA COFFEE BEANS", "Id": "292"}, {"Name": "<PERSON><PERSON>, Mirella", "Id": "57"}, {"Name": "TOMATO SAUCE 500G", "Id": "142"}, {"Name": "UGANDA PILI PILI", "Id": "353"}, {"Name": "Vanilla Sticks", "Id": "199"}, {"Name": "Visitors Coat", "Id": "151"}, {"Name": "water 330ml", "Id": "269"}, {"Name": "Water Bottle 19 L", "Id": "66"}, {"Name": "Water Bottle 500ml", "Id": "268"}, {"Name": "Yeast", "Id": "122"}, {"Name": "Yest", "Id": "88"}, {"Name": "Paper & Packaging", "Id": "18"}, {"Name": "24.2x41+5.5+4CM,55MC,Whole Wheat FP-LDPR-SS-003-Bread", "Id": "203"}, {"Name": "24.2x41+6CM,55MI<PERSON>, White Bread FP-LDPR-SS-004-Bread", "Id": "204"}, {"Name": "25x41+6+4CM,60MC, TP, Country Bread FP-LDPR-SS-006-Bread", "Id": "197"}, {"Name": "25x41+6+4CM,60MC, TP, Wholewheat FP-LDPR-SS-005-Bread", "Id": "198"}, {"Name": "32x40CM,55MIC,TRP,D2W,Plain,FP-LDPL-BS-008-Bread", "Id": "231"}, {"Name": "32x50CM,55MIC,TRP,D2W,Plain,FP-LDPL-BS-009-Bread", "Id": "352"}, {"Name": "Ancient Grains Ingredients Stickers", "Id": "330"}, {"Name": "Ancient Grains Name Stickers", "Id": "329"}, {"Name": "Artisan Sourdough Tag", "Id": "240"}, {"Name": "Baguette <PERSON>g", "Id": "227"}, {"Name": "Baking Paper Sheets 60cm x 40 cm", "Id": "184"}, {"Name": "Barcode Stickers (3 Kind artworks)", "Id": "266"}, {"Name": "Broiche Bun <PERSON>", "Id": "274"}, {"Name": "Brown Pizza Boxes 28x28 CM", "Id": "53"}, {"Name": "Brown Square Bottom Paper Bags 23x13x37 cm", "Id": "257"}, {"Name": "Country Bread Ingredients Stickers", "Id": "282"}, {"Name": "Country bread Product Name Sticker", "Id": "278"}, {"Name": "Die Cut Handle Bag Small (1x500)", "Id": "19"}, {"Name": "Face Mask 3PLY 50x40 With Melt Down Filter Blue", "Id": "230"}, {"Name": "Face Mask W/Ear Loop (Non Woven-Blue)", "Id": "24"}, {"Name": "Falcon Cling Film 300 (1700) x 45 cm (Export)", "Id": "25"}, {"Name": "Freezer Zipper Bag", "Id": "92"}, {"Name": "French Rye Ingredients Stickers", "Id": "281"}, {"Name": "French Rye Product Name Sticker", "Id": "277"}, {"Name": "Garbage Bag 110x130 cm", "Id": "26"}, {"Name": "Ingredients Stickers ( 2 Kind artworks)", "Id": "264"}, {"Name": "<PERSON> (Blue Roll) 25 x 50 Cm 100x12", "Id": "186"}, {"Name": "Jars 500ml for Olives", "Id": "358"}, {"Name": "Love Bread Again Carry Bags", "Id": "226"}, {"Name": "Love Bread Black Sticker", "Id": "261"}, {"Name": "Maxipull 2 Ply", "Id": "22"}, {"Name": "Multi Seed Ingredients Stickers", "Id": "283"}, {"Name": "Multi Seed Product Name Sticker", "Id": "280"}, {"Name": "Olive Pit Warning Sticker", "Id": "340"}, {"Name": "Olives Jar - 550ML", "Id": "347"}, {"Name": "Organic Country Bread Sticker", "Id": "259"}, {"Name": "Organic French Rye Bread Sticker", "Id": "260"}, {"Name": "Organic Whole Wheat Sticker", "Id": "258"}, {"Name": "Palesitnin Olive and Zatar bread product Name Stickers", "Id": "338"}, {"Name": "Palestinian Olive and Zatar bread Ingredient Sticker", "Id": "339"}, {"Name": "Paper Bag Plain w/out Barcode", "Id": "284"}, {"Name": "Paper Bag White Flat Handle 34x18x33.5cm", "Id": "273"}, {"Name": "Paper bag with handle brown", "Id": "192"}, {"Name": "Paper Bag With <PERSON><PERSON> Brown (1x250)", "Id": "20"}, {"Name": "Pizza Box Liners Medium", "Id": "54"}, {"Name": "Plain Bag - LD 32 cms x 40 cmsg", "Id": "149"}, {"Name": "Plain Bag - LD 32 cms x 50 cms", "Id": "148"}, {"Name": "Plain Plastic bags", "Id": "161"}, {"Name": "Plastic Sandwich Bag", "Id": "90"}, {"Name": "Product Name Stickers (3 Kind Artworks)", "Id": "265"}, {"Name": "Round Stickers White", "Id": "314"}, {"Name": "Round Stickers( 1Kind Artwork)", "Id": "267"}, {"Name": "Side Sealing Bags (37*25) Full Print Blue", "Id": "71"}, {"Name": "Side Sealing Bags (37*25) Full Print Orange", "Id": "72"}, {"Name": "Sourdough 3 Kind Bags", "Id": "228"}, {"Name": "Sticker Round- Digital Printing", "Id": "239"}, {"Name": "Sticker-Round", "Id": "232"}, {"Name": "<PERSON><PERSON> (L) Powder Free", "Id": "23"}, {"Name": "Warm Me up, <PERSON>", "Id": "275"}, {"Name": "White Ribbon With Black Letters", "Id": "262"}, {"Name": "Whole Wheat Product Name Sticker", "Id": "279"}, {"Name": "Pizza", "Id": "38"}, {"Name": "Frozen Flatbread Base with Cheese 25cm x 15 cm", "Id": "132"}, {"Name": "Frozen Flatbread Base with <PERSON><PERSON> - 260", "Id": "121"}, {"Name": "Frozen Flatbread Base with <PERSON><PERSON> 25cm x 15 cm", "Id": "131"}, {"Name": "Frozen Pizza Base - 260", "Id": "137"}, {"Name": "Frozen Pizza Base 15 cm", "Id": "134"}, {"Name": "Frozen Pizza base 28 cm", "Id": "133"}, {"Name": "Frozen Pizza Base with Cheese", "Id": "101"}, {"Name": "Frozen Pizza Base with <PERSON>ato Sauce - 175", "Id": "123"}, {"Name": "Frozen Pizza Base with <PERSON>ato Sa<PERSON> - 260", "Id": "77"}, {"Name": "Frozen Pizza Base with <PERSON>ato Sauce - 280", "Id": "100"}, {"Name": "Frozen Pizza Base with Tomato Sauce - R", "Id": "82"}, {"Name": "FULLY COOKED PLAIN BASE 650G", "Id": "219"}, {"Name": "Pizza - Margherita", "Id": "39"}, {"Name": "Pizza - Pepperoni", "Id": "40"}, {"Name": "Pizza - Vegetarian", "Id": "120"}, {"Name": "PIZZA FROZEN BASE W/ TOMATO SAUCE 650G", "Id": "139"}, {"Name": "WHITE PIZZA FROZEN BASE W/ CHEESE 850G", "Id": "143"}, {"Name": "Pizza Boxes Deposit", "Id": "234"}, {"Name": "Pizza Doough", "Id": "158"}, {"Name": "PODIUM, HG & BIN FEES", "Id": "247"}, {"Name": "Portal Charges", "Id": "237"}, {"Name": "PURCHASE ACHIEVEMENT FEES FIXED", "Id": "244"}, {"Name": "PURCHASE SERVICE FEES", "Id": "243"}, {"Name": "Sales", "Id": "1"}, {"Name": "Service Charges", "Id": "196"}, {"Name": "SPECIAL EVENT PROMO FEES", "Id": "248"}, {"Name": "Spinneys E-Commerce", "Id": "238"}, {"Name": "SSIP Portal Charges", "Id": "236"}, {"Name": "STRIPE CHARGES", "Id": "298"}, {"Name": "SUPPLIER AUDIT CHARG-Bakery", "Id": "253"}, {"Name": "T Choitrams E-Commerce", "Id": "271"}, {"Name": "ZIINA CHARGES", "Id": "315"}], "terms": [{"Name": "30 days from month end", "DueDays": null, "DueNextMonthDays": 30, "Id": "6"}, {"Name": "45 days from month end", "DueDays": null, "DueNextMonthDays": 45, "Id": "14"}, {"Name": "60 days from month end", "DueDays": null, "DueNextMonthDays": 60, "Id": "4"}, {"Name": "Advance", "DueDays": 0, "DueNextMonthDays": null, "Id": "8"}, {"Name": "Cash due on receipt", "DueDays": 0, "DueNextMonthDays": null, "Id": "1"}, {"Name": "End of month", "DueDays": null, "DueNextMonthDays": null, "Id": "12"}, {"Name": "FOC", "DueDays": 1, "DueNextMonthDays": null, "Id": "9"}, {"Name": "Net 30", "DueDays": 30, "DueNextMonthDays": null, "Id": "3"}, {"Name": "<PERSON><PERSON>", "DueDays": 0, "DueNextMonthDays": null, "Id": "7"}]}