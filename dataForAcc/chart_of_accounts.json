[{"Name": "Accounting Services", "FullyQualifiedName": "Accounting Services", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Accounts Payable (A/P)", "FullyQualifiedName": "Accounts Payable (A/P)", "Classification": "Liability", "AccountType": "Accounts Payable", "AccountSubType": "Accounts<PERSON>ayable", "CurrentBalance": -644027.57, "Description": null}, {"Name": "Accounts Receivable (A/R)", "FullyQualifiedName": "Accounts Receivable (A/R)", "Classification": "<PERSON><PERSON>", "AccountType": "Accounts Receivable", "AccountSubType": "AccountsReceivable", "CurrentBalance": 994996.07, "Description": null}, {"Name": "Accrued holiday payable", "FullyQualifiedName": "Accrued holiday payable", "Classification": null, "AccountType": "Long Term Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Accrued liabilities", "FullyQualifiedName": "Accrued liabilities", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Accrued non-current liabilities", "FullyQualifiedName": "Accrued non-current liabilities", "Classification": null, "AccountType": "Long Term Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Accumulated Depreciation", "FullyQualifiedName": "Accumulated Depreciation", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": 0, "Description": null}, {"Name": "CCTV Cameras", "FullyQualifiedName": "Accumulated Depreciation:CCTV Cameras", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": -10505.33, "Description": "Accumulated Depreciation"}, {"Name": "Computer and Office Equipment", "FullyQualifiedName": "Accumulated Depreciation:Computer and Office Equipment", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": -4641.49, "Description": null}, {"Name": "Fit Out", "FullyQualifiedName": "Accumulated Depreciation:Fit Out", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": -384059.42, "Description": "Accumulated Depreciation"}, {"Name": "Kitchen Equipment", "FullyQualifiedName": "Accumulated Depreciation:Kitchen Equipment", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": -424242.95, "Description": null}, {"Name": "Signage", "FullyQualifiedName": "Accumulated Depreciation:Signage", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "AccumulatedDepreciation", "CurrentBalance": -4000, "Description": "Accumulated Depreciation"}, {"Name": "Additional <PERSON><PERSON>", "FullyQualifiedName": "Additional <PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON> on Salik", "FullyQualifiedName": "<PERSON><PERSON> on Salik", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Administrative Salaries", "FullyQualifiedName": "Administrative Salaries", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Advance to Staff", "FullyQualifiedName": "Advance to Staff", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 1671.5, "Description": null}, {"Name": "Advance to Sufyan - Driver", "FullyQualifiedName": "Advance to Staff:Advance to Sufyan - Driver", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": "Advance to Sufyan - Driver on Traffic Fines"}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON><PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Amanat Advance", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 5900, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "Naveed Advance", "FullyQualifiedName": "Advance to Staff:Naveed Advance", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "Other current assets", "FullyQualifiedName": "Advance to Staff:Other current assets", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": "<PERSON>"}, {"Name": "<PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Advance to Staff:<PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "EmployeeCashAdvances", "CurrentBalance": 0, "Description": null}, {"Name": "Advance to Suppliers", "FullyQualifiedName": "Advance to Suppliers", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Advances", "FullyQualifiedName": "Advances", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Advertisement Fee Yearly", "FullyQualifiedName": "Advertisement Fee Yearly", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Advertising/Promotional", "FullyQualifiedName": "Advertising/Promotional", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON> (Sats)", "FullyQualifiedName": "<PERSON><PERSON> (Sats)", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": -500, "Description": null}, {"Name": "Air Tickets", "FullyQualifiedName": "Air Tickets", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Al Ansari Exchange Charges", "FullyQualifiedName": "Al Ansari Exchange Charges", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Allowance for bad debt", "FullyQualifiedName": "Allowance for bad debt", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "AllowanceForBadDebts", "CurrentBalance": 0, "Description": null}, {"Name": "Amazon Web Services", "FullyQualifiedName": "Amazon Web Services", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Amortisation expense", "FullyQualifiedName": "Amortisation expense", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Artisan Sourdough Tag", "FullyQualifiedName": "Artisan Sourdough Tag", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Audit Expenses", "FullyQualifiedName": "Audit Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Available for sale assets (short-term)", "FullyQualifiedName": "Available for sale assets (short-term)", "Classification": null, "AccountType": "Other Current Asset", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Bad debts", "FullyQualifiedName": "Bad debts", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "BadDebts", "CurrentBalance": 0, "Description": null}, {"Name": "Bank charges", "FullyQualifiedName": "Bank charges", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "BankCharges", "CurrentBalance": 0, "Description": null}, {"Name": "Billable Expense", "FullyQualifiedName": "Billable Expense", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Billable Expense Income", "FullyQualifiedName": "Billable Expense Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Block charges", "FullyQualifiedName": "Block charges", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Bourgeois Flour Consumption", "FullyQualifiedName": "Bourgeois Flour Consumption", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Brainstorm Workshop", "FullyQualifiedName": "Brainstorm Workshop", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Branding", "FullyQualifiedName": "Branding", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Business Insurance", "FullyQualifiedName": "Business Insurance", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Insurance", "CurrentBalance": 0, "Description": null}, {"Name": "Business Revaluation", "FullyQualifiedName": "Business Revaluation", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Car Wash Expenses", "FullyQualifiedName": "Car Wash Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RepairMaintenance", "CurrentBalance": 0, "Description": null}, {"Name": "Cash on hand", "FullyQualifiedName": "Cash on hand", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "CashOnHand", "CurrentBalance": 24441.68, "Description": "Cash on hand"}, {"Name": "Cash With <PERSON><PERSON>", "FullyQualifiedName": "Cash on hand:Cash With <PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "CashOnHand", "CurrentBalance": -389.31, "Description": null}, {"Name": "Cash With <PERSON><PERSON>", "FullyQualifiedName": "Cash on hand:Cash With <PERSON><PERSON>:Cash With <PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "CashOnHand", "CurrentBalance": -1, "Description": null}, {"Name": "Cash with <PERSON><PERSON><PERSON>", "FullyQualifiedName": "Cash with <PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "CashOnHand", "CurrentBalance": 0, "Description": null}, {"Name": "Certification and Compliance Expenses", "FullyQualifiedName": "Certification and Compliance Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": "Certification and Compliance Expenses"}, {"Name": "Change in inventory - COS", "FullyQualifiedName": "Change in inventory - COS", "Classification": null, "AccountType": "Cost of Goods Sold", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Chaser", "FullyQualifiedName": "Chaser", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "<PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Cleaning Stuff", "FullyQualifiedName": "Cleaning Stuff", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Commission Deliveroo", "FullyQualifiedName": "Commission Deliveroo", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Commission Income", "FullyQualifiedName": "Commission Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Commissions and fees", "FullyQualifiedName": "Commissions and fees", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Cost of sales", "FullyQualifiedName": "Cost of sales", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Country Bread Stickers", "FullyQualifiedName": "Country Bread Stickers", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Covid - 19 PCR Test", "FullyQualifiedName": "Covid - 19 PCR Test", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Customer Engagement Fees", "FullyQualifiedName": "Customer Engagement Fees", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Damage & Expire Stock", "FullyQualifiedName": "Damage & Expire Stock", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Deferred Revenue", "FullyQualifiedName": "Deferred Revenue", "Classification": "Liability", "AccountType": "Other Current Liability", "AccountSubType": "DeferredRevenue", "CurrentBalance": 0, "Description": null}, {"Name": "Deferred tax assets", "FullyQualifiedName": "Deferred tax assets", "Classification": null, "AccountType": "Other Asset", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Depreciation", "FullyQualifiedName": "Depreciation", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "Depreciation", "CurrentBalance": 0, "Description": null}, {"Name": "Dewa Expenses", "FullyQualifiedName": "Dewa Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Utilities", "CurrentBalance": 0, "Description": null}, {"Name": "Dewa Power Upgrade", "FullyQualifiedName": "Dewa Power Upgrade", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RepairMaintenance", "CurrentBalance": 0, "Description": null}, {"Name": "Digital Ocean", "FullyQualifiedName": "Digital Ocean", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Discounts given", "FullyQualifiedName": "Discounts given", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "DiscountsRefundsGiven", "CurrentBalance": 0, "Description": null}, {"Name": "Discounts given - COS", "FullyQualifiedName": "Discounts given - COS", "Classification": null, "AccountType": "Cost of Goods Sold", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Di<PERSON>lay <PERSON><PERSON>", "FullyQualifiedName": "Di<PERSON>lay <PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Dividend disbursed", "FullyQualifiedName": "Dividend disbursed", "Classification": null, "AccountType": "Equity", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Dividend income", "FullyQualifiedName": "Dividend income", "Classification": "Revenue", "AccountType": "Other Income", "AccountSubType": "DividendIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Dividends payable", "FullyQualifiedName": "Dividends payable", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Dropbox", "FullyQualifiedName": "Dropbox", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Dubai Real Estate Exp", "FullyQualifiedName": "Dubai Real Estate Exp", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RentOrLeaseOfBuildings", "CurrentBalance": 0, "Description": null}, {"Name": "Dues and subscriptions", "FullyQualifiedName": "Dues and subscriptions", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "DuesSubscriptions", "CurrentBalance": 0, "Description": null}, {"Name": "Ecommerce Fees", "FullyQualifiedName": "Ecommerce Fees", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "End of Services Benifits", "FullyQualifiedName": "End of Services Benifits", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "OtherMiscellaneousExpense", "CurrentBalance": 0, "Description": "End of Services Benifits"}, {"Name": "Equipment Expenses", "FullyQualifiedName": "Equipment Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RepairMaintenance", "CurrentBalance": 0, "Description": null}, {"Name": "Equipment rental", "FullyQualifiedName": "Equipment rental", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "EquipmentRental", "CurrentBalance": 0, "Description": null}, {"Name": "Equity in earnings of subsidiaries", "FullyQualifiedName": "Equity in earnings of subsidiaries", "Classification": null, "AccountType": "Equity", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Fire Safety Maintenance Exp", "FullyQualifiedName": "Fire Safety Maintenance Exp", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Food Tasting", "FullyQualifiedName": "Food Tasting", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Freight and delivery - COS", "FullyQualifiedName": "Freight and delivery - COS", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "ShippingFreightDeliveryCos", "CurrentBalance": 0, "Description": null}, {"Name": "Google Cloud - GSuite", "FullyQualifiedName": "Google Cloud - GSuite", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Gratuity Expenses", "FullyQualifiedName": "Gratuity Expenses", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "HACCP Certification", "FullyQualifiedName": "HACCP Certification", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Income tax expense", "FullyQualifiedName": "Income tax expense", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Insurance - Disability", "FullyQualifiedName": "Insurance - Disability", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Insurance", "CurrentBalance": 0, "Description": null}, {"Name": "Insurance - General", "FullyQualifiedName": "Insurance - General", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Insurance", "CurrentBalance": 0, "Description": null}, {"Name": "Insurance - Liability", "FullyQualifiedName": "Insurance - Liability", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Insurance", "CurrentBalance": 0, "Description": null}, {"Name": "Intangibles", "FullyQualifiedName": "Intangibles", "Classification": null, "AccountType": "Other Asset", "AccountSubType": null, "CurrentBalance": 11055, "Description": null}, {"Name": "Interest earned", "FullyQualifiedName": "Interest earned", "Classification": "Revenue", "AccountType": "Other Income", "AccountSubType": "InterestEarned", "CurrentBalance": 0, "Description": null}, {"Name": "Interest expense", "FullyQualifiedName": "Interest expense", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "InterestPaid", "CurrentBalance": 0, "Description": null}, {"Name": "Interest income", "FullyQualifiedName": "Interest income", "Classification": "Revenue", "AccountType": "Other Income", "AccountSubType": "InterestEarned", "CurrentBalance": 0, "Description": null}, {"Name": "Internet & Telephone Expenses", "FullyQualifiedName": "Internet & Telephone Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Utilities", "CurrentBalance": 0, "Description": null}, {"Name": "*********", "FullyQualifiedName": "Internet & Telephone Expenses:*********", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Utilities", "CurrentBalance": 0, "Description": null}, {"Name": "*********", "FullyQualifiedName": "Internet & Telephone Expenses:*********", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Utilities", "CurrentBalance": 0, "Description": null}, {"Name": "Intrest Income", "FullyQualifiedName": "Intrest Income", "Classification": "Revenue", "AccountType": "Other Income", "AccountSubType": "InterestEarned", "CurrentBalance": 0, "Description": null}, {"Name": "Inventory", "FullyQualifiedName": "Inventory", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Inventory - Packing Material", "FullyQualifiedName": "Inventory - Packing Material", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "Inventory", "CurrentBalance": 77363.22, "Description": null}, {"Name": "Inventory Asset", "FullyQualifiedName": "Inventory Asset", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "Inventory", "CurrentBalance": 307891.55, "Description": null}, {"Name": "Inventory Shrinkage", "FullyQualifiedName": "Inventory Shrinkage", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Kangaroo Setup Fee", "FullyQualifiedName": "Kangaroo Setup Fee", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Leave Encashment", "FullyQualifiedName": "Leave Encashment", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Legal and professional fees", "FullyQualifiedName": "Legal and professional fees", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Liabilities related to assets held for sale", "FullyQualifiedName": "Liabilities related to assets held for sale", "Classification": null, "AccountType": "Long Term Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Loan from Related Parties - <PERSON><PERSON>", "FullyQualifiedName": "Loan from Related Parties - <PERSON><PERSON>", "Classification": "Liability", "AccountType": "Long Term Liability", "AccountSubType": "OtherLongTermLiabilities", "CurrentBalance": -2648695.01, "Description": "Loan from Related Parties - <PERSON><PERSON>"}, {"Name": "Loan from Related Parties - Salem R Al <PERSON>", "FullyQualifiedName": "Loan from Related Parties - Salem R Al <PERSON>", "Classification": "Liability", "AccountType": "Long Term Liability", "AccountSubType": "OtherLongTermLiabilities", "CurrentBalance": -338645, "Description": "Loan from Related Parties - Salem R Al <PERSON>"}, {"Name": "Local Flour Consumption", "FullyQualifiedName": "Local Flour Consumption", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Logo Design", "FullyQualifiedName": "Logo Design", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Long-term debt", "FullyQualifiedName": "Long-term debt", "Classification": null, "AccountType": "Long Term Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Long-Term Investments", "FullyQualifiedName": "Long-Term Investments", "Classification": null, "AccountType": "Other Asset", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Loss on discontinued operations, net of tax", "FullyQualifiedName": "Loss on discontinued operations, net of tax", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Loss on disposal of assets", "FullyQualifiedName": "Loss on disposal of assets", "Classification": null, "AccountType": "Other Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Management compensation", "FullyQualifiedName": "Management compensation", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Marketing Expenses", "FullyQualifiedName": "Marketing Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Digital Marketing- Right Media", "FullyQualifiedName": "Marketing Expenses:Digital Marketing- Right Media", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": "Right Media"}, {"Name": "Email marketing automation", "FullyQualifiedName": "Marketing Expenses:Email marketing automation", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Facebook Ads", "FullyQualifiedName": "Marketing Expenses:Facebook Ads", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Google Ads", "FullyQualifiedName": "Marketing Expenses:Google Ads", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Onfleet", "FullyQualifiedName": "Marketing Expenses:Onfleet", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Photoshoot Content -Wessam WHMB", "FullyQualifiedName": "Marketing Expenses:Photoshoot Content -Wessam WHMB", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": "Wessam WHMB"}, {"Name": "Retainer- Studio Foreign", "FullyQualifiedName": "Marketing Expenses:Retainer- Studio Foreign", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": "Studio Foreign"}, {"Name": "Social Media - <PERSON><PERSON><PERSON>", "FullyQualifiedName": "Marketing Expenses:Social Media - <PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Social Media Conten Creation - <PERSON><PERSON>", "FullyQualifiedName": "Marketing Expenses:Social Media Conten Creation - <PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Marketing Hub Starter", "FullyQualifiedName": "Marketing Hub Starter", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Materials - COS", "FullyQualifiedName": "Materials - COS", "Classification": null, "AccountType": "Cost of Goods Sold", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Meals and entertainment", "FullyQualifiedName": "Meals and entertainment", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "EntertainmentMeals", "CurrentBalance": 0, "Description": null}, {"Name": "Medical Insurance Exp", "FullyQualifiedName": "Medical Insurance Exp", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Insurance", "CurrentBalance": 0, "Description": null}, {"Name": "Misc Food Items", "FullyQualifiedName": "Misc Food Items", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Missive", "FullyQualifiedName": "Missive", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Network International", "FullyQualifiedName": "Network International", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Network International Charges", "FullyQualifiedName": "Network International Charges", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "FinanceCosts", "CurrentBalance": 0, "Description": null}, {"Name": "Non Cash to Net off", "FullyQualifiedName": "Non Cash to Net off", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 0, "Description": "To net of the balances"}, {"Name": "Non Food Items Consumption", "FullyQualifiedName": "Non Food Items Consumption", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Noqodi Charges", "FullyQualifiedName": "Noqodi Charges", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "OtherMiscellaneousExpense", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON><PERSON><PERSON>", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 43.44, "Description": null}, {"Name": "Odoo Charges", "FullyQualifiedName": "Odoo Charges", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "DuesSubscriptions", "CurrentBalance": 0, "Description": null}, {"Name": "Office expenses", "FullyQualifiedName": "Office expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Grease Trap Cleaning", "FullyQualifiedName": "Office expenses:Grease Trap Cleaning", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>ses", "FullyQualifiedName": "Office expenses:<PERSON><PERSON><PERSON>penses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Pest Control Exp", "FullyQualifiedName": "Office expenses:Pest Control Exp", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Onfleet System Expense", "FullyQualifiedName": "Onfleet System Expense", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": "Onfleet Delivery System Monthly Subscription"}, {"Name": "Opening Balance Equity", "FullyQualifiedName": "Opening Balance Equity", "Classification": "Equity", "AccountType": "Equity", "AccountSubType": "OpeningBalanceEquity", "CurrentBalance": 0, "Description": null}, {"Name": "Other - COS", "FullyQualifiedName": "Other - COS", "Classification": null, "AccountType": "Cost of Goods Sold", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Other Charges -<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other Charges -<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Other costs of sales - COS", "FullyQualifiedName": "Other costs of sales - COS", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": "SSIP Portal Charges"}, {"Name": "Other Expense", "FullyQualifiedName": "Other Expense", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "OtherMiscellaneousExpense", "CurrentBalance": 0, "Description": null}, {"Name": "Other Food Items Consumption", "FullyQualifiedName": "Other Food Items Consumption", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Other general and administrative expenses", "FullyQualifiedName": "Other general and administrative expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Other operating income (expenses)", "FullyQualifiedName": "Other operating income (expenses)", "Classification": null, "AccountType": "Other Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Other selling expenses", "FullyQualifiedName": "Other selling expenses", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:Aeh<PERSON>zaz", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Distribution Charges", "FullyQualifiedName": "Other selling expenses:Distribution Charges", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>veed <PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Other selling expenses:<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Other Types of Expenses-Advertising Expenses", "FullyQualifiedName": "Other Types of Expenses-Advertising Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Owners' Current Account - <PERSON><PERSON>", "FullyQualifiedName": "Owners' Current Account - <PERSON><PERSON>", "Classification": "Equity", "AccountType": "Equity", "AccountSubType": "PartnersEquity", "CurrentBalance": -869505.64, "Description": "Owners' Current Account - <PERSON><PERSON>"}, {"Name": "Owners' Current Account - Salem R <PERSON>", "FullyQualifiedName": "Owners' Current Account - Salem R <PERSON>", "Classification": "Equity", "AccountType": "Equity", "AccountSubType": "PartnersEquity", "CurrentBalance": -320977.37, "Description": "Owners' Current Account - Salem R <PERSON>"}, {"Name": "<PERSON><PERSON>O <PERSON> Fee", "FullyQualifiedName": "<PERSON><PERSON>O <PERSON> Fee", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "DuesSubscriptions", "CurrentBalance": 0, "Description": null}, {"Name": "Packing Material Consumption", "FullyQualifiedName": "Packing Material Consumption", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Parking Expenses", "FullyQualifiedName": "Parking Expenses", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Payroll Clearing", "FullyQualifiedName": "Payroll Clearing", "Classification": "Liability", "AccountType": "Other Current Liability", "AccountSubType": "PayrollClearing", "CurrentBalance": 0, "Description": null}, {"Name": "Payroll liabilities", "FullyQualifiedName": "Payroll liabilities", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Pemo Charges", "FullyQualifiedName": "Pemo Charges", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "OtherMiscellaneousExpense", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON><PERSON>", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 1355.8, "Description": null}, {"Name": "Podium, HG & Bin Fees", "FullyQualifiedName": "Podium, HG & Bin Fees", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid expenses", "FullyQualifiedName": "Prepaid expenses", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Odoo Prepaid Expenses", "FullyQualifiedName": "Prepaid expenses:Odoo Prepaid Expenses", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid AC Maintenance", "FullyQualifiedName": "Prepaid expenses:Prepaid AC Maintenance", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 8333.44, "Description": null}, {"Name": "Prepaid Air Ticket", "FullyQualifiedName": "Prepaid expenses:Prepaid Air Ticket", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": "One Time Prepaid Air Ticket"}, {"Name": "Prepaid Block Charges", "FullyQualifiedName": "Prepaid expenses:Prepaid Block Charges", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Business Insurance", "FullyQualifiedName": "Prepaid expenses:Prepaid Business Insurance", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 7500.56, "Description": null}, {"Name": "Prepaid Certification, Audit and Compliance Expenses", "FullyQualifiedName": "Prepaid expenses:Prepaid Certification, Audit and Compliance Expenses", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 315.81, "Description": "Prepaid Certification, Audit and Compliance Expenses"}, {"Name": "Prepaid DREC", "FullyQualifiedName": "Prepaid expenses:Prepaid DREC", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 2703.84, "Description": "Dubai Real Estate Corporation Tax"}, {"Name": "Prepaid Dropbox", "FullyQualifiedName": "Prepaid expenses:Prepaid Dropbox", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 716.17, "Description": null}, {"Name": "Prepaid Establishment Card", "FullyQualifiedName": "Prepaid expenses:Prepaid Establishment Card", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 508.36, "Description": null}, {"Name": "Prepaid Fire Safety AMC", "FullyQualifiedName": "Prepaid expenses:Prepaid Fire Safety AMC", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Food Permit", "FullyQualifiedName": "Prepaid expenses:Prepaid Food Permit", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Food Watch", "FullyQualifiedName": "Prepaid expenses:Prepaid Food Watch", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 84.16, "Description": null}, {"Name": "Prepaid Marketing Automation", "FullyQualifiedName": "Prepaid expenses:Prepaid Marketing Automation", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Medical Insurance", "FullyQualifiedName": "Prepaid expenses:Prepaid Medical Insurance", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 4656.72, "Description": null}, {"Name": "Prepaid Odoo Subscription", "FullyQualifiedName": "Prepaid expenses:Prepaid Odoo Subscription", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 32166.98, "Description": null}, {"Name": "Prepaid OH Card", "FullyQualifiedName": "Prepaid expenses:Prepaid OH Card", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 1878.31, "Description": null}, {"Name": "Prepaid P.O Box Rent", "FullyQualifiedName": "Prepaid expenses:Prepaid P.O Box Rent", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 580.25, "Description": null}, {"Name": "Prepaid Pest Control", "FullyQualifiedName": "Prepaid expenses:Prepaid Pest Control", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 2789.62, "Description": null}, {"Name": "Prepaid Quickbook", "FullyQualifiedName": "Prepaid expenses:Prepaid Quickbook", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Road Permit", "FullyQualifiedName": "Prepaid expenses:Prepaid Road Permit", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0.09, "Description": null}, {"Name": "Prepaid Shopify License (Loyalty program)", "FullyQualifiedName": "Prepaid expenses:Prepaid Shopify License (Loyalty program)", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Prepaid Social Media Marketing", "FullyQualifiedName": "Prepaid expenses:Prepaid Social Media Marketing", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 0, "Description": "Hootsuite"}, {"Name": "Prepaid Trade License", "FullyQualifiedName": "Prepaid expenses:Prepaid Trade License", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 3986.7, "Description": null}, {"Name": "Prepaid Vehicles Rent", "FullyQualifiedName": "Prepaid expenses:Prepaid Vehicles Rent", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 6400, "Description": null}, {"Name": "Prepaid Visa Expenses", "FullyQualifiedName": "Prepaid expenses:Prepaid Visa Expenses", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 108880.98, "Description": null}, {"Name": "Prepaid Warehouse Maintenance", "FullyQualifiedName": "Prepaid expenses:Prepaid Warehouse Maintenance", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 1170, "Description": null}, {"Name": "Prepaid Warehouse Rent", "FullyQualifiedName": "Prepaid expenses:Prepaid Warehouse Rent", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 6666.7, "Description": null}, {"Name": "Qashio Subscription", "FullyQualifiedName": "Prepaid expenses:Qashio Subscription", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "PrepaidExpenses", "CurrentBalance": 3917.32, "Description": null}, {"Name": "Price Difference Nutritional Test", "FullyQualifiedName": "Price Difference Nutritional Test", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Property, plant and equipment", "FullyQualifiedName": "Property, plant and equipment", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "OtherFixedAssets", "CurrentBalance": 0, "Description": null}, {"Name": "CCTV Cameras", "FullyQualifiedName": "Property, plant and equipment:CCTV Cameras", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "OtherFixedAssets", "CurrentBalance": 12760, "Description": null}, {"Name": "Computer and Office Equipment", "FullyQualifiedName": "Property, plant and equipment:Computer and Office Equipment", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "OtherFixedAssets", "CurrentBalance": 4915.78, "Description": null}, {"Name": "Fit Out", "FullyQualifiedName": "Property, plant and equipment:Fit Out", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "Buildings", "CurrentBalance": 536050.13, "Description": null}, {"Name": "Kitchen Equipments", "FullyQualifiedName": "Property, plant and equipment:Kitchen Equipments", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "MachineryAndEquipment", "CurrentBalance": 1043261.22, "Description": null}, {"Name": "Office Equipments", "FullyQualifiedName": "Property, plant and equipment:Office Equipments", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "OtherFixedAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Signage", "FullyQualifiedName": "Property, plant and equipment:Signage", "Classification": "<PERSON><PERSON>", "AccountType": "Fixed Asset", "AccountSubType": "OtherFixedAssets", "CurrentBalance": 4000, "Description": null}, {"Name": "Provision for Audit", "FullyQualifiedName": "Provision for Audit", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Provision for End of Services Benifits", "FullyQualifiedName": "Provision for End of Services Benifits", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": -118051, "Description": "Provision for End of Services Benifits"}, {"Name": "Purchase Achievement Fees Fixed", "FullyQualifiedName": "Purchase Achievement Fees Fixed", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Purchase Service Fee", "FullyQualifiedName": "Purchase Service Fee", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Purchases", "FullyQualifiedName": "Purchases", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "M Tork Paper 2 Ply", "FullyQualifiedName": "Purchases:M Tork Paper 2 Ply", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Qashio Transaction charges/ Subscription", "FullyQualifiedName": "Qashio Transaction charges/ Subscription", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "BankCharges", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON> wallet", "FullyQualifiedName": "<PERSON><PERSON><PERSON> wallet", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "Checking", "CurrentBalance": 34360.34, "Description": null}, {"Name": "Quickbooks Fee", "FullyQualifiedName": "Quickbooks Fee", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "RAK BANK", "FullyQualifiedName": "RAK BANK", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "Checking", "CurrentBalance": 485405.46, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON>", "Classification": null, "AccountType": "Equity", "AccountSubType": "PartnerContributions", "CurrentBalance": 59376.67, "Description": null}, {"Name": "Rawteen Commissions", "FullyQualifiedName": "Rawteen Commissions", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Recipe Content", "FullyQualifiedName": "Recipe Content", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Reconciliation Discrepancies", "FullyQualifiedName": "Reconciliation Discrepancies", "Classification": "Expense", "AccountType": "Other Expense", "AccountSubType": "OtherMiscellaneousExpense", "CurrentBalance": 0, "Description": null}, {"Name": "Refund Amount", "FullyQualifiedName": "Refund Amount", "Classification": "Liability", "AccountType": "Credit Card", "AccountSubType": "CreditCard", "CurrentBalance": 0, "Description": null}, {"Name": "Repairs and Maintenance", "FullyQualifiedName": "Repairs and Maintenance", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RepairMaintenance", "CurrentBalance": 0, "Description": null}, {"Name": "Retained Earnings", "FullyQualifiedName": "Retained Earnings", "Classification": "Equity", "AccountType": "Equity", "AccountSubType": "RetainedEarnings", "CurrentBalance": 0, "Description": null}, {"Name": "Retainer- Studio Foreignio", "FullyQualifiedName": "Retainer- Studio Foreignio", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Revenue - General", "FullyQualifiedName": "Revenue - General", "Classification": null, "AccountType": "Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON>", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Salaries", "FullyQualifiedName": "Salaries", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Salaries COS", "FullyQualifiedName": "Salaries COS", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Driver", "FullyQualifiedName": "Salaries COS:Driver", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Production Staff", "FullyQualifiedName": "Salaries COS:Production Staff", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Bharat", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Bibek Nepal", "FullyQualifiedName": "Salaries COS:Production Staff:Bibek Nepal", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Chaitanya Bapurao", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Chet", "FullyQualifiedName": "Salaries COS:Production Staff:Chet", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "JOHN KETH", "FullyQualifiedName": "Salaries COS:Production Staff:JOHN KETH", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "MARK", "FullyQualifiedName": "Salaries COS:Production Staff:MARK", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Nar", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>r", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "R<PERSON>wan", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Rizad", "FullyQualifiedName": "Salaries COS:Production Staff:R<PERSON>d", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "Salaries COS:Production Staff:<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Salaries Payable", "FullyQualifiedName": "Salaries Payable", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": -172484.03, "Description": null}, {"Name": "Salary Payable to <PERSON><PERSON>", "FullyQualifiedName": "Salary Payable to <PERSON><PERSON>", "Classification": "Liability", "AccountType": "Long Term Liability", "AccountSubType": "OtherLongTermLiabilities", "CurrentBalance": 0, "Description": null}, {"Name": "Sales", "FullyQualifiedName": "Sales", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Sales - retail", "FullyQualifiedName": "Sales - retail", "Classification": null, "AccountType": "Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Sales - wholesale", "FullyQualifiedName": "Sales - wholesale", "Classification": null, "AccountType": "Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Sales of Product Income", "FullyQualifiedName": "Sales of Product Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON>", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Security Deposits", "FullyQualifiedName": "Security Deposits", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 0, "Description": null}, {"Name": "Dewa Security", "FullyQualifiedName": "Security Deposits:Dewa Security", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 2000, "Description": null}, {"Name": "Dubai Municipality Security Deposit", "FullyQualifiedName": "Security Deposits:Dubai Municipality Security Deposit", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 15000, "Description": null}, {"Name": "Vehicles Security Deposit", "FullyQualifiedName": "Security Deposits:Vehicles Security Deposit", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 12800, "Description": null}, {"Name": "Visa's Security", "FullyQualifiedName": "Security Deposits:Visa's Security", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 18000, "Description": null}, {"Name": "Warehouse Security", "FullyQualifiedName": "Security Deposits:Warehouse Security", "Classification": "<PERSON><PERSON>", "AccountType": "Other Asset", "AccountSubType": "SecurityDeposits", "CurrentBalance": 18726, "Description": null}, {"Name": "Share capital", "FullyQualifiedName": "Share capital", "Classification": null, "AccountType": "Equity", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON>", "FullyQualifiedName": "Share capital:<PERSON><PERSON>aw<PERSON>", "Classification": null, "AccountType": "Equity", "AccountSubType": null, "CurrentBalance": -3000, "Description": null}, {"Name": "<PERSON>", "FullyQualifiedName": "Share capital:<PERSON>", "Classification": null, "AccountType": "Equity", "AccountSubType": null, "CurrentBalance": -297000, "Description": null}, {"Name": "Shipping Income", "FullyQualifiedName": "Shipping Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Shippment Clearing Expenses", "FullyQualifiedName": "Shippment Clearing Expenses", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Shopify", "FullyQualifiedName": "Shopify", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Shopify License (loyalty Program)", "FullyQualifiedName": "Shopify License (loyalty Program)", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Short-term debit", "FullyQualifiedName": "Short-term debit", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Social Media Handling", "FullyQualifiedName": "Social Media Handling", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Special Event Promo Fees", "FullyQualifiedName": "Special Event Promo Fees", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Spinneys E-Commerce", "FullyQualifiedName": "Spinneys E-Commerce", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "SSIP Portal Charges", "FullyQualifiedName": "SSIP Portal Charges", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Staff Food", "FullyQualifiedName": "Staff Food", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Staff Uniform", "FullyQualifiedName": "Staff Uniform", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "CostOfLabor", "CurrentBalance": 0, "Description": null}, {"Name": "Stationery and printing", "FullyQualifiedName": "Stationery and printing", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OfficeGeneralAdministrativeExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Stickers/Labels", "FullyQualifiedName": "Stickers/Labels", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Storage Charges", "FullyQualifiedName": "Storage Charges", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Stripe", "FullyQualifiedName": "Stripe", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "STRIPE charges", "FullyQualifiedName": "STRIPE charges", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Supplier Audit", "FullyQualifiedName": "Supplier Audit", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Supplier Audit Charg-Bakery", "FullyQualifiedName": "Supplier Audit Charg-Bakery", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Supplier Registration Expenses", "FullyQualifiedName": "Supplier Registration Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "T Choithrams E-Commerce", "FullyQualifiedName": "T Choithrams E-Commerce", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "The Pizza Guys Restaurant LLC", "FullyQualifiedName": "The Pizza Guys Restaurant LLC", "Classification": "Liability", "AccountType": "Accounts Payable", "AccountSubType": "Accounts<PERSON>ayable", "CurrentBalance": 0, "Description": null}, {"Name": "Tips Payable", "FullyQualifiedName": "Tips Payable", "Classification": null, "AccountType": "Other Current Liability", "AccountSubType": null, "CurrentBalance": 35168.99, "Description": null}, {"Name": "Trade License Exp", "FullyQualifiedName": "Trade License Exp", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Traffic Fines", "FullyQualifiedName": "Traffic Fines", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Traning Expenses", "FullyQualifiedName": "Traning Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Travel expenses - general and admin expenses", "FullyQualifiedName": "Travel expenses - general and admin expenses", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Travel expenses - selling expenses", "FullyQualifiedName": "Travel expenses - selling expenses", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Delivery <PERSON>", "FullyQualifiedName": "Travel expenses - selling expenses:Delivery Van Rent", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "<PERSON><PERSON><PERSON>", "FullyQualifiedName": "<PERSON><PERSON><PERSON>", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Unapplied Cash Bill Payment Expense", "FullyQualifiedName": "Unapplied Cash Bill Payment Expense", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "UnappliedCashBillPaymentExpense", "CurrentBalance": 0, "Description": null}, {"Name": "Unapplied Cash Payment Income", "FullyQualifiedName": "Unapplied Cash Payment Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "UnappliedCashPaymentIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Asset", "FullyQualifiedName": "Uncategorised Asset", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Asset ( 74 )", "FullyQualifiedName": "Uncategorised Asset ( 74 )", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "OtherCurrentAssets", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Expense", "FullyQualifiedName": "Uncategorised Expense", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OtherMiscellaneousServiceCost", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Expense ( 78 )", "FullyQualifiedName": "Uncategorised Expense ( 78 )", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "OtherMiscellaneousServiceCost", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Income", "FullyQualifiedName": "Uncategorised Income", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Uncategorised Income ( 77 )", "FullyQualifiedName": "Uncategorised Income ( 77 )", "Classification": "Revenue", "AccountType": "Income", "AccountSubType": "SalesOfProductIncome", "CurrentBalance": 0, "Description": null}, {"Name": "Undeposited Funds", "FullyQualifiedName": "Undeposited Funds", "Classification": "<PERSON><PERSON>", "AccountType": "Other Current Asset", "AccountSubType": "UndepositedFunds", "CurrentBalance": 0, "Description": null}, {"Name": "United Car Rentals", "FullyQualifiedName": "United Car Rentals", "Classification": "Liability", "AccountType": "Accounts Payable", "AccountSubType": "Accounts<PERSON>ayable", "CurrentBalance": 0, "Description": null}, {"Name": "Unrealised loss on securities, net of tax", "FullyQualifiedName": "Unrealised loss on securities, net of tax", "Classification": null, "AccountType": "Other Income", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Urban Food Commercial Fees", "FullyQualifiedName": "Urban Food Commercial Fees", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Advertising Fees", "FullyQualifiedName": "Urban Food Commercial Fees:Advertising Fees", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Govt Promotion Fee", "FullyQualifiedName": "Urban Food Commercial Fees:Govt Promotion Fee", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "OtherCostsOfServiceCos", "CurrentBalance": 0, "Description": null}, {"Name": "Opening store fee", "FullyQualifiedName": "Urban Food Commercial Fees:Opening store fee", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "CostOfLaborCos", "CurrentBalance": 0, "Description": null}, {"Name": "Utilities", "FullyQualifiedName": "Utilities", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "Utilities", "CurrentBalance": 0, "Description": null}, {"Name": "VAT Control", "FullyQualifiedName": "VAT Control", "Classification": "Liability", "AccountType": "Other Current Liability", "AccountSubType": "GlobalTaxPayable", "CurrentBalance": 9686.82, "Description": null}, {"Name": "VAT Payable/(Refundable)", "FullyQualifiedName": "VAT Payable/(Refundable)", "Classification": "Liability", "AccountType": "Other Current Liability", "AccountSubType": "GlobalTaxSuspense", "CurrentBalance": -94652.81, "Description": null}, {"Name": "Vehicle Fuel-51035", "FullyQualifiedName": "Vehicle Fuel-51035", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Vehicle Fuel-Canter", "FullyQualifiedName": "Vehicle Fuel-Canter", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "Visa Cost", "FullyQualifiedName": "Visa Cost", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Visa Expenses", "FullyQualifiedName": "Visa Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "LegalProfessionalFees", "CurrentBalance": 0, "Description": null}, {"Name": "Wage expenses", "FullyQualifiedName": "Wage expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "PayrollExpenses", "CurrentBalance": 0, "Description": null}, {"Name": "Warehouse Rent", "FullyQualifiedName": "Warehouse Rent", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "RentOrLeaseOfBuildings", "CurrentBalance": 0, "Description": null}, {"Name": "Water Advances", "FullyQualifiedName": "Water Advances", "Classification": null, "AccountType": "Bank", "AccountSubType": null, "CurrentBalance": 2069.46, "Description": null}, {"Name": "Water for Dough", "FullyQualifiedName": "Water for Dough", "Classification": "Expense", "AccountType": "Cost of Goods Sold", "AccountSubType": "SuppliesMaterialsCogs", "CurrentBalance": 0, "Description": null}, {"Name": "Website Expenses", "FullyQualifiedName": "Website Expenses", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Wio Bank", "FullyQualifiedName": "Wio Bank", "Classification": "<PERSON><PERSON>", "AccountType": "Bank", "AccountSubType": "Checking", "CurrentBalance": 159655.2, "Description": null}, {"Name": "Yamm.com", "FullyQualifiedName": "Yamm.com", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": "Mail merge Mass mail service at a time"}, {"Name": "ZAPIER", "FullyQualifiedName": "ZAPIER", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}, {"Name": "Ziina Charges", "FullyQualifiedName": "Ziina Charges", "Classification": null, "AccountType": "Expense", "AccountSubType": null, "CurrentBalance": 0, "Description": null}, {"Name": "ZOHO Corporation", "FullyQualifiedName": "ZOHO Corporation", "Classification": "Expense", "AccountType": "Expense", "AccountSubType": "AdvertisingPromotional", "CurrentBalance": 0, "Description": null}]