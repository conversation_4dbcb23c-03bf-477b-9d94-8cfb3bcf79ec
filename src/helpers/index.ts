import type { AccountI } from "@/types/account";
import type { TrialBalanceI } from "@/types/trailBalanceReport";

export function filter_relevant_accounts(
  chart_of_accounts: AccountI[],
  trial_balance_data: TrialBalanceI,
) {
  const activityLookup = trial_balance_data.Rows?.Row

  const filtered_accounts: AccountWithActivity[] = [];

  for (const account of chart_of_accounts) {
    const account_name = account.account_name;
    const current_balance = Math.abs(account.current_balance || 0);
    const activity = activityLookup[account_name] || {};
    const total_debits = activity.total_debits_12m || 0;
    const total_credits = activity.total_credits_12m || 0;
    const total_activity = total_debits + total_credits;

    let include_account = false;

    if (total_activity >= 500) {
      include_account = true;
    } else if (current_balance >= 100) {
      include_account = true;
    } else if (is_inherently_cash_related(account)) {
      include_account = true;
    }

    if (is_non_cash_account(account)) {
      include_account = false;
    }

    if (account.account_type?.toLowerCase() === "equity") {
      include_account = false;
    }

    if (include_account) {
      filtered_accounts.push({
        ...account,
        total_debits_12m: total_debits,
        total_credits_12m: total_credits,
      });
    }
  }

  return filtered_accounts;
}

function is_inherently_cash_related(account: Account): boolean {
  const account_type = account.account_type?.toLowerCase() || "";
  const account_sub_type = account.account_sub_type?.toLowerCase() || "";
  const account_name = account.account_name?.toLowerCase() || "";

  const cash_keywords = [
    "bank",
    "cash",
    "checking",
    "savings",
    "petty cash",
    "money market",
  ];
  if (cash_keywords.some((keyword) => account_name.includes(keyword))) {
    return true;
  }

  const credit_keywords = [
    "credit card",
    "line of credit",
    "visa",
    "mastercard",
    "amex",
  ];
  if (credit_keywords.some((keyword) => account_name.includes(keyword))) {
    return true;
  }

  if (
    account_sub_type.includes("receivable") ||
    account_sub_type.includes("payable")
  ) {
    return true;
  }

  if (account_sub_type.includes("loan") || account_sub_type.includes("note")) {
    return true;
  }

  return false;
}

function is_non_cash_account(account: Account): boolean {
  const account_name = account.account_name?.toLowerCase() || "";
  const account_sub_type = account.account_sub_type?.toLowerCase() || "";

  const non_cash_keywords = [
    "depreciation",
    "amortization",
    "accumulated depreciation",
    "unrealized",
    "contra",
    "allowance for doubtful",
  ];

  return non_cash_keywords.some(
    (keyword) =>
      account_name.includes(keyword) || account_sub_type.includes(keyword),
  );
}
