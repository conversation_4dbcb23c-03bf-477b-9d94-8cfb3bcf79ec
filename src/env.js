import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    AUTH_SECRET:
      process.env.NODE_ENV === "production"
        ? z.string()
        : z.string().optional(),
    QB_CLIENT_ID: z.string(),
    QB_CLIENT_SECRET: z.string(),
    SHOPIFY_CLIENT_ID: z.string(),
    SHOPIFY_CLIENT_SECRET: z.string(),
    GEMINI_API_KEY: z.string(),
    DATABASE_URL: z.string().url(),
    AMAZON_AWS_ACCESS_KEY_ID: z.string(),
    AMAZON_AWS_SECRET_ACCESS_KEY: z.string(),
    QB_ENV: z.string(),
    QB_BASE_URL: z.string(),
    AMAZON_AWS_REGION: z.string(),
    AMAZON_AWS_S3_BUCKET_NAME: z.string(),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    AUTH_SECRET: process.env.AUTH_SECRET,
    QB_CLIENT_ID: process.env.QB_CLIENT_ID,
    QB_CLIENT_SECRET: process.env.QB_CLIENT_SECRET,
    SHOPIFY_CLIENT_ID: process.env.SHOPIFY_CLIENT_ID,
    SHOPIFY_CLIENT_SECRET: process.env.SHOPIFY_CLIENT_SECRET,
    DATABASE_URL: process.env.DATABASE_URL,
    QB_BASE_URL: process.env.QB_BASE_URL,
    QB_ENV: process.env.QB_ENV,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    AMAZON_AWS_ACCESS_KEY_ID: process.env.AMAZON_AWS_ACCESS_KEY_ID,
    AMAZON_AWS_SECRET_ACCESS_KEY: process.env.AMAZON_AWS_SECRET_ACCESS_KEY,
    AMAZON_AWS_REGION: process.env.AMAZON_AWS_REGION,
    AMAZON_AWS_S3_BUCKET_NAME: process.env.AMAZON_AWS_S3_BUCKET_NAME,
    NODE_ENV: process.env.NODE_ENV,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
});
