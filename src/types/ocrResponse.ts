interface RootBillSalesTermRef {
  value?: string;
}

interface RootBillVendorRef {
  value?: string;
}

interface RootBillLineItemBasedExpenseLineDetailItemRef {
  value?: string;
}

interface RootBillLineItemBasedExpenseLineDetailTaxCodeRef {
  value?: string;
}

interface RootBillLineItemBasedExpenseLineDetail {
  UnitPrice?: number;
  Qty?: number;
  ItemRef?: RootBillLineItemBasedExpenseLineDetailItemRef;
  TaxCodeRef?: RootBillLineItemBasedExpenseLineDetailTaxCodeRef;
}

interface RootBillLine {
  DetailType?: string;
  Description?: string;
  Amount?: number;
  ItemBasedExpenseLineDetail?: RootBillLineItemBasedExpenseLineDetail;
}

interface RootBill {
  PrivateNote?: string;
  TxnDate?: string;
  DueDate?: string;
  GlobalTaxCalculation?: string;
  SalesTermRef?: RootBillSalesTermRef;
  VendorRef?: RootBillVendorRef;
  Line?: RootBillLine[];
}

interface RootVendors {
  DisplayName: string;
  Id: string;
}

interface RootTaxes {
  name: string;
  id: string;
  rate: number;
}

interface RootItems {
  Name: string;
  Id: string;
}

interface RootTerms {
  Name: string;
  Id: string;
  Type: string;
  DayOfMonthDue: number;
}

export interface OcrResponseI {
  bill?: RootBill;
  vendors?: RootVendors[];
  taxes?: RootTaxes[];
  items?: RootItems[];
  terms?: RootTerms[];
}
