"use client";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { signIn, useSession } from "next-auth/react";
import Link from "next/link";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { api } from "@/trpc/react";

export default function Page() {
  const router = useRouter();
  const session = useSession();
  // const {} = api.quickbooks.getBoardingState
  const [form, setForm] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { data: userData, refetch } = api.user.getUserInformation.useQuery();

  useEffect(() => {
    if (session.status === "authenticated") {
      alert(JSON.stringify(session));
      refetch();
    }
  }, [session.status, router]);
  useEffect(() => {
    if (userData) {
      if (!userData.length) {
        router.push("/boarding");
      }
      if (userData[0] && userData.length === 1) {
        // check his boarding state
        if (!userData[0]?.UserCompanyState?.done) {
          router.push(`/boarding/${userData[0].companyId}`);
        } else {
          router.push(`/company/${userData[0].companyId}`);
        }
      } else {
        router.push("/company");
      }
    }
  }, [userData]);

  if (userData) {
    console.log("userData", userData);
  }
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <div className={cn("flex flex-col gap-6")}>
          <Card className="overflow-hidden">
            <CardContent className="grid p-0 md:grid-cols-2">
              <form
                className="p-6 md:p-8"
                onSubmit={async (e) => {
                  e.preventDefault();
                  setIsLoading(true);
                  setError("");
                  try {
                    const result = await signIn("credentials", {
                      email: form.email,
                      password: form.password,
                      callbackUrl: "/boarding",
                      redirect: false,
                    });
                    console.log(result);

                    if (result?.error) {
                      setError("Invalid email or password");
                    }
                    // window.location.href = "/boarding";
                  } catch (err) {
                    console.error(err);
                    setError("An error occurred during login");
                  } finally {
                    setIsLoading(false);
                  }
                }}
              >
                <div className="flex flex-col gap-6">
                  <div className="flex flex-col items-center text-center">
                    <h1 className="text-2xl font-bold">Welcome back</h1>

                    <p className="text-muted-foreground text-balance">
                      Login to your Wilfredo account
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      onChange={(e) =>
                        setForm({ ...form, email: e.target.value })
                      }
                      value={form.email}
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <div className="flex items-center">
                      <Label htmlFor="password">Password</Label>
                      <a
                        href="#"
                        className="ml-auto text-sm underline-offset-2 hover:underline"
                      >
                        Forgot your password?
                      </a>
                    </div>
                    <Input
                      onChange={(e) =>
                        setForm({ ...form, password: e.target.value })
                      }
                      value={form.password}
                      id="password"
                      type="password"
                      required
                    />
                  </div>
                  {error && (
                    <div className="mb-2 text-sm text-red-500">{error}</div>
                  )}
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Logging in..." : "Login"}
                  </Button>

                  <div className="text-center text-sm">
                    Don&apos;t have an account?{" "}
                    <Link
                      href="/signup"
                      className="text-primary hover:text-primary/90 underline underline-offset-4"
                    >
                      Sign up
                    </Link>
                  </div>
                </div>
              </form>
              <div className="bg-muted relative hidden md:block">
                <Image
                  width={500}
                  height={500}
                  src="/wilfredoLogo.png"
                  alt="Logo"
                  className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
                />
              </div>
            </CardContent>
          </Card>
          <div className="text-muted-foreground hover:[&_a]:text-primary text-center text-xs text-balance [&_a]:underline [&_a]:underline-offset-4">
            By clicking continue, you agree to our{" "}
            <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
}
