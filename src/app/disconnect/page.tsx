import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ExternalLink, RefreshCw } from "lucide-react";
import { api } from "@/trpc/server";

export default async function DisconnectedPage() {
  try {
    await api.quickbooks.disconnectCompanies();
  } catch (e) {
    console.log(e);
  }
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        <div className="mb-8 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="rounded-full bg-gray-900 p-2">
              <span className="text-xl font-bold text-white">W</span>
            </div>
            <h1 className="text-2xl font-bold">Wilfredo</h1>
          </div>
        </div>

        <Card className="border-gray-200 shadow-lg">
          <CardHeader className="pb-4">
            <div className="mb-2 flex justify-center">
              <div className="rounded-full bg-blue-100 p-4">
                <RefreshCw className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <CardTitle className="text-center text-xl">
              QuickBooks Disconnected
            </CardTitle>
            <CardDescription className="text-center">
              You've successfully disconnected Wilfredo from your QuickBooks
              account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pb-6">
            <div className="rounded-lg bg-gray-100 p-4 text-sm text-gray-800">
              <p>
                Your QuickBooks data is no longer syncing with Wilfredo. Any
                automated tasks or integrations that relied on QuickBooks data
                have been paused.
              </p>
            </div>
            <div className="space-y-2 text-sm">
              <p className="font-medium">What this means:</p>
              <ul className="list-inside list-disc space-y-1 pl-2">
                <li>Wilfredo no longer has access to your QuickBooks data</li>
                <li>
                  Any automated workflows using QuickBooks data are paused
                </li>
                <li>
                  All of your data is deleted from Wilfredo and no longer
                  accessible
                </li>
              </ul>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-3">
            <Button className="w-full" size="lg" asChild>
              <Link href="/boarding">
                {" "}
                <RefreshCw className="mr-2 h-4 w-4" />
                Reconnect to QuickBooks
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            Changed your mind? You can reconnect to QuickBooks at any time from
            your{" "}
            <Link
              href="/account/integrations"
              className="inline-flex items-center text-blue-600 hover:underline"
            >
              account integrations page
              <ExternalLink className="ml-1 h-3 w-3" />
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
