"use client";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  CheckCircle2,
  ChevronRight,
  Code2,
  CreditCard,
  Globe,
  Headphones,
  Laptop,
  LayoutGrid,
  Lightbulb,
  MessageSquare,
  Rocket,
  Zap,
} from "lucide-react";
import { useEffect } from "react";

export default function LandingPage() {
  useEffect(() => {
    window.location.href = "/login";
  }, []);
  return (
    <div className="flex min-h-screen w-screen flex-col justify-center">
      <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40 w-full border-b px-10 backdrop-blur">
        <div className="flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
          <div className="flex gap-6 md:gap-10">
            <Link href="/" className="flex items-center space-x-2">
              <Rocket className="h-6 w-6" />
              <span className="inline-block font-bold">Wilfredo</span>
            </Link>
            <nav className="hidden gap-6 md:flex">
              <Link
                href="#features"
                className="text-muted-foreground hover:text-primary flex items-center text-sm font-medium transition-colors"
              >
                Features
              </Link>
              <Link
                href="#how-it-works"
                className="text-muted-foreground hover:text-primary flex items-center text-sm font-medium transition-colors"
              >
                How It Works
              </Link>
              <Link
                href="#pricing"
                className="text-muted-foreground hover:text-primary flex items-center text-sm font-medium transition-colors"
              >
                Pricing
              </Link>
              <Link
                href="#faq"
                className="text-muted-foreground hover:text-primary flex items-center text-sm font-medium transition-colors"
              >
                FAQ
              </Link>
            </nav>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-4">
            <nav className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/signup">Sign Up</Link>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <main className="flex-1 ring-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48">
          <div className="container mx-auto px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    Transform the way you work with our powerful app
                  </h1>
                  <p className="text-muted-foreground max-w-[600px] md:text-xl">
                    Streamline your workflow, boost productivity, and achieve
                    more with our intuitive platform designed for modern teams.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button size="lg" asChild>
                    <Link href="/signup">
                      Get Started <ChevronRight className="ml-1 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <Link href="#demo">Watch Demo</Link>
                  </Button>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <CheckCircle2 className="text-primary h-4 w-4" />
                    <span>No credit card required</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CheckCircle2 className="text-primary h-4 w-4" />
                    <span>14-day free trial</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-center">
                <div className="bg-background relative aspect-video overflow-hidden rounded-xl border p-2 shadow-xl">
                  <Image
                    src="/placeholder.svg?height=720&width=1280"
                    width={1280}
                    height={720}
                    alt="App screenshot"
                    className="rounded-lg object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Logos Section */}
        <section className="w-full border-t border-b py-12 md:py-16 lg:py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-xl font-medium tracking-tight">
                  Trusted by companies worldwide
                </h2>
              </div>
              <div className="flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-center">
                    <Image
                      src="/placeholder.svg?height=40&width=140"
                      width={140}
                      height={40}
                      alt={`Company logo ${i + 1}`}
                      className="opacity-70 grayscale transition-all hover:opacity-100 hover:grayscale-0"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-12 md:py-24 lg:py-32">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="bg-muted inline-block rounded-lg px-3 py-1 text-sm">
                  Features
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Everything you need to succeed
                </h2>
                <p className="text-muted-foreground max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Our platform provides all the tools you need to streamline
                  your workflow, collaborate with your team, and achieve your
                  goals.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-2 lg:gap-10">
              <Image
                src="/placeholder.svg?height=550&width=550"
                width={550}
                height={550}
                alt="Features illustration"
                className="mx-auto aspect-square overflow-hidden rounded-xl object-cover object-center sm:w-full lg:order-last"
              />
              <div className="flex flex-col justify-center space-y-4">
                <ul className="grid gap-6">
                  <li>
                    <div className="grid gap-1">
                      <div className="flex items-center gap-2">
                        <Zap className="text-primary h-5 w-5" />
                        <h3 className="text-xl font-bold">Lightning Fast</h3>
                      </div>
                      <p className="text-muted-foreground">
                        Experience unparalleled speed with our optimized
                        platform, designed for efficiency.
                      </p>
                    </div>
                  </li>
                  <li>
                    <div className="grid gap-1">
                      <div className="flex items-center gap-2">
                        <Laptop className="text-primary h-5 w-5" />
                        <h3 className="text-xl font-bold">Cross-Platform</h3>
                      </div>
                      <p className="text-muted-foreground">
                        Access your work from any device, anywhere, with our
                        seamless cross-platform experience.
                      </p>
                    </div>
                  </li>
                  <li>
                    <div className="grid gap-1">
                      <div className="flex items-center gap-2">
                        <LayoutGrid className="text-primary h-5 w-5" />
                        <h3 className="text-xl font-bold">Customizable</h3>
                      </div>
                      <p className="text-muted-foreground">
                        Tailor the platform to your specific needs with our
                        flexible customization options.
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <Globe className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">Global Access</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Access your work from anywhere in the world with our
                    cloud-based platform.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <MessageSquare className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">Team Collaboration</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Work together seamlessly with your team in real-time with
                    our collaboration tools.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <Lightbulb className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">Smart Automation</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Automate repetitive tasks and focus on what matters most
                    with our intelligent workflows.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <Code2 className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">Developer API</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Extend functionality with our comprehensive API for
                    developers and integrations.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <Headphones className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">24/7 Support</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Get help whenever you need it with our dedicated support
                    team available around the clock.
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CreditCard className="text-primary h-6 w-6" />
                  <CardTitle className="mt-2">Secure Payments</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Rest easy knowing your transactions are protected with our
                    secure payment processing.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section
          id="how-it-works"
          className="bg-muted w-full py-12 md:py-24 lg:py-32"
        >
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="bg-background inline-block rounded-lg px-3 py-1 text-sm">
                  How It Works
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Simple, powerful, and intuitive
                </h2>
                <p className="text-muted-foreground max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Our platform is designed to be easy to use while providing
                  powerful features to help you achieve your goals.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-3">
              <div className="flex flex-col items-center space-y-2 border-b pb-4 md:border-r md:border-b-0 md:pr-4 md:pb-0">
                <div className="bg-primary text-primary-foreground flex h-12 w-12 items-center justify-center rounded-full text-lg font-bold">
                  1
                </div>
                <h3 className="text-xl font-bold">Sign Up</h3>
                <p className="text-muted-foreground text-center">
                  Create your account in seconds and get started with your free
                  trial.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border-b pb-4 md:border-r md:border-b-0 md:pr-4 md:pb-0">
                <div className="bg-primary text-primary-foreground flex h-12 w-12 items-center justify-center rounded-full text-lg font-bold">
                  2
                </div>
                <h3 className="text-xl font-bold">Set Up</h3>
                <p className="text-muted-foreground text-center">
                  Configure your workspace and invite your team members to
                  collaborate.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2">
                <div className="bg-primary text-primary-foreground flex h-12 w-12 items-center justify-center rounded-full text-lg font-bold">
                  3
                </div>
                <h3 className="text-xl font-bold">Succeed</h3>
                <p className="text-muted-foreground text-center">
                  Start using the platform to streamline your workflow and
                  achieve your goals.
                </p>
              </div>
            </div>
            <div className="mx-auto max-w-3xl">
              <div className="bg-background relative aspect-video overflow-hidden rounded-xl border shadow-xl">
                <Image
                  src="/placeholder.svg?height=720&width=1280"
                  width={1280}
                  height={720}
                  alt="Product demo"
                  className="object-cover"
                  id="demo"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-background/80 backdrop-blur-sm"
                  >
                    Watch Demo
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="bg-muted inline-block rounded-lg px-3 py-1 text-sm">
                  Testimonials
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Loved by businesses worldwide
                </h2>
                <p className="text-muted-foreground max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Don't just take our word for it. Here's what our customers
                  have to say about our platform.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="border-0 shadow-md">
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="bg-muted h-10 w-10 overflow-hidden rounded-full">
                        <Image
                          src="/placeholder.svg?height=40&width=40"
                          width={40}
                          height={40}
                          alt={`Testimonial author ${i + 1}`}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div>
                        <CardTitle className="text-base">
                          Customer Name
                        </CardTitle>
                        <CardDescription>Position, Company</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      "This platform has completely transformed how our team
                      works. We've seen a 30% increase in productivity since we
                      started using it."
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section
          id="pricing"
          className="bg-muted w-full py-12 md:py-24 lg:py-32"
        >
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="bg-background inline-block rounded-lg px-3 py-1 text-sm">
                  Pricing
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Simple, transparent pricing
                </h2>
                <p className="text-muted-foreground max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Choose the plan that's right for you and get started with your
                  14-day free trial.
                </p>
              </div>
            </div>
            <div className="mx-auto max-w-5xl py-12">
              <Tabs defaultValue="monthly" className="w-full">
                <div className="flex justify-center">
                  <TabsList>
                    <TabsTrigger value="monthly">Monthly</TabsTrigger>
                    <TabsTrigger value="annually">
                      Annually (Save 20%)
                    </TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="monthly">
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8">
                    <Card className="flex flex-col">
                      <CardHeader>
                        <CardTitle>Starter</CardTitle>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$9</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          Perfect for individuals and small projects.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Up to 5 projects</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Basic analytics</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>24-hour support response time</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Get Started</Button>
                      </CardFooter>
                    </Card>
                    <Card className="border-primary flex flex-col">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle>Pro</CardTitle>
                          <div className="bg-primary text-primary-foreground rounded-full px-2.5 py-0.5 text-xs font-semibold">
                            Popular
                          </div>
                        </div>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$29</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          Ideal for growing teams and businesses.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Unlimited projects</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Advanced analytics</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>4-hour support response time</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Team collaboration tools</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Custom integrations</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Get Started</Button>
                      </CardFooter>
                    </Card>
                    <Card className="flex flex-col">
                      <CardHeader>
                        <CardTitle>Enterprise</CardTitle>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$99</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          For large organizations with advanced needs.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Everything in Pro</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Dedicated account manager</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>1-hour support response time</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Custom training sessions</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Advanced security features</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Contact Sales</Button>
                      </CardFooter>
                    </Card>
                  </div>
                </TabsContent>
                <TabsContent value="annually">
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8">
                    <Card className="flex flex-col">
                      <CardHeader>
                        <CardTitle>Starter</CardTitle>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$7</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          Billed annually ($84/year)
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Up to 5 projects</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Basic analytics</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>24-hour support response time</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Get Started</Button>
                      </CardFooter>
                    </Card>
                    <Card className="border-primary flex flex-col">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle>Pro</CardTitle>
                          <div className="bg-primary text-primary-foreground rounded-full px-2.5 py-0.5 text-xs font-semibold">
                            Popular
                          </div>
                        </div>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$23</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          Billed annually ($276/year)
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Unlimited projects</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Advanced analytics</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>4-hour support response time</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Team collaboration tools</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Custom integrations</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Get Started</Button>
                      </CardFooter>
                    </Card>
                    <Card className="flex flex-col">
                      <CardHeader>
                        <CardTitle>Enterprise</CardTitle>
                        <div className="flex items-baseline gap-1">
                          <span className="text-3xl font-bold">$79</span>
                          <span className="text-muted-foreground">/month</span>
                        </div>
                        <CardDescription>
                          Billed annually ($948/year)
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="flex flex-1 flex-col">
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Everything in Pro</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Dedicated account manager</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>1-hour support response time</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Custom training sessions</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <CheckCircle2 className="text-primary h-4 w-4" />
                            <span>Advanced security features</span>
                          </li>
                        </ul>
                      </CardContent>
                      <CardFooter>
                        <Button className="w-full">Contact Sales</Button>
                      </CardFooter>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="w-full py-12 md:py-24 lg:py-32">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <div className="bg-muted inline-block rounded-lg px-3 py-1 text-sm">
                  FAQ
                </div>
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Frequently asked questions
                </h2>
                <p className="text-muted-foreground max-w-[900px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Find answers to common questions about our platform.
                </p>
              </div>
            </div>
            <div className="mx-auto max-w-3xl space-y-4 py-12">
              {[
                {
                  question: "How does the 14-day free trial work?",
                  answer:
                    "Our 14-day free trial gives you full access to all features of the Pro plan. No credit card is required to sign up. At the end of the trial, you can choose to subscribe to a paid plan or downgrade to our free tier.",
                },
                {
                  question: "Can I change my plan later?",
                  answer:
                    "Yes, you can upgrade, downgrade, or cancel your plan at any time. If you upgrade, the new rate will be charged on your next billing cycle. If you downgrade, you'll continue to have access to your current plan until the end of your billing period.",
                },
                {
                  question:
                    "Is there a limit to how many team members I can add?",
                  answer:
                    "The Starter plan allows up to 5 team members. The Pro plan allows up to 20 team members. The Enterprise plan has unlimited team members. If you need more team members on the Starter or Pro plans, you can add them for an additional fee.",
                },
                {
                  question:
                    "Do you offer discounts for nonprofits or educational institutions?",
                  answer:
                    "Yes, we offer special pricing for nonprofits, educational institutions, and open-source projects. Please contact our sales team for more information.",
                },
                {
                  question: "How secure is your platform?",
                  answer:
                    "Security is our top priority. We use industry-standard encryption, regular security audits, and follow best practices to ensure your data is safe. Our platform is SOC 2 compliant and we offer additional security features for Enterprise customers.",
                },
              ].map((item, i) => (
                <Card key={i}>
                  <CardHeader>
                    <CardTitle>{item.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{item.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-muted w-full py-12 md:py-24 lg:py-32">
          <div className="container mx-auto px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Ready to get started?
                </h2>
                <p className="text-muted-foreground max-w-[600px] md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                  Join thousands of satisfied customers who are already using
                  our platform to streamline their workflow.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Button size="lg" asChild>
                  <Link href="/signup">
                    Start Your Free Trial{" "}
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">Contact Sales</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-background w-full border-t py-6 md:py-12">
        <div className="container mx-auto flex flex-col items-center justify-center gap-4 px-4 md:flex-row md:gap-8 md:px-6">
          <div className="flex items-center space-x-2">
            <Rocket className="h-6 w-6" />
            <span className="font-bold">AppName</span>
          </div>
          <nav className="flex gap-4 sm:gap-6">
            <Link
              href="#"
              className="text-sm underline-offset-4 hover:underline"
            >
              Terms
            </Link>
            <Link
              href="#"
              className="text-sm underline-offset-4 hover:underline"
            >
              Privacy
            </Link>
            <Link
              href="#"
              className="text-sm underline-offset-4 hover:underline"
            >
              Cookies
            </Link>
          </nav>
          <div className="text-muted-foreground flex-1 text-center text-sm md:text-right">
            © {new Date().getFullYear()} AppName Inc. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
