"use client";
import React from "react";
import { CheckCircle, RefreshCw, AlertCircle } from "lucide-react";

const VerificationDashboard = () => {
  // Simulation of verification progress - in real implementation, this would come from your API
  const [verificationProgress, setVerificationProgress] = React.useState(0);
  const [currentStep, setCurrentStep] = React.useState(
    "Connecting to QuickBooks...",
  );
  const [isComplete, setIsComplete] = React.useState(false);

  const steps = [
    "Connecting to QuickBooks...",
    "Downloading transaction data...",
    "Processing account balances...",
    "Calculating cash flows...",
    "Verifying data accuracy...",
    "Complete!",
  ];

  React.useEffect(() => {
    if (verificationProgress < 100) {
      const timer = setTimeout(() => {
        const newProgress = Math.min(verificationProgress + 20, 100);
        setVerificationProgress(newProgress);
        const stepIndex = Math.floor((newProgress / 100) * (steps.length - 1));
        setCurrentStep(steps[stepIndex] as any);

        if (newProgress >= 100) {
          setIsComplete(true);
        }
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [verificationProgress]);

  // Mock data - in real implementation, this would come from your API
  const verificationData = [
    {
      metric: "Total Cash on Hand",
      payflowValue: "$47,328.15",
      quickbooksValue: "$47,328.15",
      status: "verified",
      description: "Sum of all cash accounts",
    },
    {
      metric: "Outstanding A/R",
      payflowValue: "$23,847.92",
      quickbooksValue: "$23,847.92",
      status: "verified",
      description: "Total accounts receivable",
    },
    {
      metric: "Outstanding A/P",
      payflowValue: "$18,294.37",
      quickbooksValue: "$18,294.37",
      status: "verified",
      description: "Total accounts payable",
    },
    {
      metric: "Net Cash Movement (30 days)",
      payflowValue: "+$8,420.18",
      quickbooksValue: "+$8,420.18",
      status: "verified",
      description: "Cash inflows minus outflows",
    },
    {
      metric: "Invoices Paid (30 days)",
      payflowValue: "47 invoices",
      quickbooksValue: "47 invoices",
      status: "verified",
      description: "Recent invoice payments",
    },
    {
      metric: "Bills Paid (30 days)",
      payflowValue: "23 bills",
      quickbooksValue: "23 bills",
      status: "verified",
      description: "Recent bill payments",
    },
  ];

  const getStatusIcon = (status: any) => {
    switch (status) {
      case "verified":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "syncing":
        return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  const allVerified = verificationData.every(
    (item) => item.status === "verified",
  );

  return (
    <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
      {/* Header with Progress */}
      <div className="mb-6 rounded-xl bg-white p-6 shadow-sm">
        <div className="mb-4">
          <h1 className="mb-2 text-2xl font-bold text-gray-900">
            {isComplete
              ? "QuickBooks Connection Verified"
              : "Setting Up Your PayFlow Dashboard"}
          </h1>
          <p className="text-gray-600">
            {isComplete
              ? "We've successfully connected to your QuickBooks and verified data accuracy"
              : "We're connecting to QuickBooks and setting up your cash flow insights"}
          </p>
        </div>

        {/* Progress Bar */}
        {!isComplete && (
          <div className="mb-4">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                {currentStep}
              </span>
              <span className="text-sm text-gray-500">
                {verificationProgress}%
              </span>
            </div>
            <div className="h-2 w-full rounded-full bg-gray-200">
              <div
                className="h-2 rounded-full bg-blue-600 transition-all duration-500 ease-out"
                style={{ width: `${verificationProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Status Icon */}
        {isComplete && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-8 w-8" />
            <span className="font-semibold">All Systems Good</span>
          </div>
        )}
      </div>

      {/* Business Health Summary - Mobile First */}
      <div className="mb-8 space-y-4">
        <div
          className={`rounded-lg bg-white p-5 shadow-sm transition-opacity duration-500 ${isComplete ? "opacity-100" : "opacity-50"}`}
        >
          <div className="mb-3 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">
              Next Major Bill
            </span>
            {isComplete && <CheckCircle className="h-5 w-5 text-green-500" />}
            {!isComplete && (
              <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
            )}
          </div>
          <div className="mb-1 text-3xl font-bold text-orange-600">
            {isComplete ? "12 days" : "---"}
          </div>
          <div className="text-sm text-gray-600">
            {isComplete
              ? "Office rent: $8,500 due Jun 19"
              : "Calculating upcoming payments..."}
          </div>
        </div>

        <div
          className={`rounded-lg bg-white p-5 shadow-sm transition-opacity duration-500 ${isComplete ? "opacity-100" : "opacity-50"}`}
        >
          <div className="mb-3 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">
              Outstanding Invoices
            </span>
            {isComplete && <CheckCircle className="h-5 w-5 text-green-500" />}
            {!isComplete && (
              <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
            )}
          </div>
          <div className="mb-1 text-3xl font-bold text-blue-600">
            {isComplete ? "$23,848" : "---"}
          </div>
          <div className="text-sm text-gray-600">
            {isComplete
              ? "Money you're owed (8 invoices)"
              : "Processing receivables..."}
          </div>
        </div>

        <div
          className={`rounded-lg bg-white p-5 shadow-sm transition-opacity duration-500 ${isComplete ? "opacity-100" : "opacity-50"}`}
        >
          <div className="mb-3 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">
              Outstanding Bills
            </span>
            {isComplete && <CheckCircle className="h-5 w-5 text-green-500" />}
            {!isComplete && (
              <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
            )}
          </div>
          <div className="mb-1 text-3xl font-bold text-red-600">
            {isComplete ? "$18,294" : "---"}
          </div>
          <div className="text-sm text-gray-600">
            {isComplete ? "Money you owe (5 bills)" : "Processing payables..."}
          </div>
        </div>
      </div>

      {/* Verification Table */}
      <div
        className={`overflow-hidden rounded-xl bg-white shadow-sm transition-opacity duration-500 ${isComplete ? "opacity-100" : "opacity-50"}`}
      >
        <div className="border-b border-gray-200 px-6 py-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Data Verification
          </h2>
          <p className="mt-1 text-sm text-gray-600">
            {isComplete
              ? "Each number matches exactly between PayFlow calculations and QuickBooks reports"
              : "Comparing our calculations with your QuickBooks data..."}
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                  Metric
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                  PayFlow Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                  QuickBooks Value
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium tracking-wider text-gray-500 uppercase">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {verificationData.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div>
                      <div className="font-medium text-gray-900">
                        {item.metric}
                      </div>
                      <div className="text-sm text-gray-500">
                        {item.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 font-mono text-sm text-gray-900">
                    {isComplete ? item.payflowValue : "---"}
                  </td>
                  <td className="px-6 py-4 font-mono text-sm text-gray-900">
                    {isComplete ? item.quickbooksValue : "---"}
                  </td>
                  <td className="px-6 py-4 text-center">
                    {isComplete ? (
                      getStatusIcon(item.status)
                    ) : (
                      <RefreshCw className="mx-auto h-5 w-5 animate-spin text-gray-400" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Next Steps */}
      {isComplete && (
        <div className="mt-6 rounded-xl bg-blue-50 p-6">
          <div className="flex items-start space-x-4">
            <CheckCircle className="mt-1 h-6 w-6 flex-shrink-0 text-blue-600" />
            <div>
              <h3 className="mb-2 font-semibold text-blue-900">
                Your Data Looks Good - PayFlow is Ready!
              </h3>
              <p className="mb-4 text-blue-800">
                We've successfully pulled your financial data from QuickBooks
                and everything checks out. You can now see cash flow forecasts,
                track your next payments, and get early warnings about cash
                crunches.
              </p>
              <button className="rounded-lg bg-blue-600 px-6 py-2 font-medium text-white transition-colors hover:bg-blue-700">
                View My Cash Flow Dashboard
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VerificationDashboard;
