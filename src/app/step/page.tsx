"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
const steps: Step[] = [
  {
    id: "welcome",
    title: "Welcome",
    content: (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Welcome to our app!</h2>
        <p className="text-muted-foreground">
          Let's get you set up in just a few steps. This won't take long.
        </p>
        <div className="bg-muted/50 rounded-lg p-4">
          <p className="text-sm">
            💡 Tip: You can navigate using the buttons in the header or at the
            bottom.
          </p>
        </div>
      </div>
    ),
  },
  {
    id: "profile",
    title: "Create Profile",
    content: (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Tell us about yourself</h2>
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium">Full Name</label>
            <input
              type="text"
              placeholder="Enter your name"
              className="mt-1 w-full rounded-md border px-3 py-2"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Email</label>
            <input
              type="email"
              placeholder="Enter your email"
              className="mt-1 w-full rounded-md border px-3 py-2"
            />
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "preferences",
    title: "Preferences",
    content: (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Set your preferences</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between rounded-lg border p-3">
            <span>Email notifications</span>
            <input type="checkbox" className="rounded" />
          </div>
          <div className="flex items-center justify-between rounded-lg border p-3">
            <span>Push notifications</span>
            <input type="checkbox" className="rounded" />
          </div>
          <div className="flex items-center justify-between rounded-lg border p-3">
            <span>Dark mode</span>
            <input type="checkbox" className="rounded" />
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "complete",
    title: "All Set!",
    content: (
      <div className="space-y-4 text-center">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
          <Check className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-xl font-semibold">You're all set!</h2>
        <p className="text-muted-foreground">
          Your account has been created successfully. You can now start using
          the app.
        </p>
      </div>
    ),
  },
];
interface Step {
  id: string;
  title: string;
  content: React.ReactNode;
}

interface MobileStepperProps {
  //   steps: Step[];
  onComplete?: () => void;
}

export default function MobileStepper({
  //   steps,
  onComplete,
}: MobileStepperProps) {
  const [currentStep, setCurrentStep] = React.useState(0);
  const [completedSteps, setCompletedSteps] = React.useState<Set<number>>(
    new Set(),
  );

  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (!isLastStep) {
      setCompletedSteps((prev) => new Set([...prev, currentStep]));
      setCurrentStep((prev) => prev + 1);
    } else {
      setCompletedSteps((prev) => new Set([...prev, currentStep]));
      onComplete?.();
    }
  };

  const handleBack = () => {
    if (!isFirstStep) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  return (
    <div className="bg-background mx-auto min-h-screen w-full max-w-screen">
      {/* Header */}
      <div className="bg-background sticky top-0 z-10 border-b">
        <div className="flex items-center justify-between p-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBack}
            disabled={isFirstStep}
            className={cn("h-10 w-10", isFirstStep && "invisible")}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <div className="flex-1 text-center">
            <h1 className="truncate px-2 text-lg font-semibold">
              {steps[currentStep]?.title}
            </h1>
            {/* Step Indicators */}
            <div className="flex justify-center">
              <div className="bg-background/80 flex gap-2 rounded-full px-3 py-2 backdrop-blur-sm">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "h-2 w-2 rounded-full transition-colors",
                      index === currentStep
                        ? "bg-primary"
                        : completedSteps.has(index)
                          ? "bg-primary/60"
                          : "bg-primary/10",
                    )}
                  />
                ))}
              </div>
            </div>
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={handleNext}
            className="h-10 w-10"
          >
            {isLastStep ? (
              <Check className="h-5 w-5" />
            ) : (
              <ChevronRight className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 pb-20">
        <Card>
          <CardContent className="p-6">
            {steps[currentStep]?.content}
          </CardContent>
        </Card>
      </div>

      {/* Bottom Navigation */}
      <div className="bg-background fixed right-0 bottom-0 left-0 border-t p-4">
        <div className="mx-auto flex max-w-md gap-3">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={isFirstStep}
            className="flex-1"
          >
            Back
          </Button>
          <Button onClick={handleNext} className="flex-1">
            {isLastStep ? "Complete" : "Next"}
          </Button>
        </div>
      </div>
    </div>
  );
}

// // Example usage component
// function StepperExample() {

//   const handleComplete = () => {
//     alert("Stepper completed!");
//   };

//   return <MobileStepper steps={steps} onComplete={handleComplete} />;
// }

// export { StepperExample };
