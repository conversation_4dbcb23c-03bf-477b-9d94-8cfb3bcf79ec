{"Header": {"Time": "2025-06-03T16:06:23Z", "ReportName": "ProfitAndLoss", "ReportBasis": "Accrual", "StartPeriod": "2025-05-01", "EndPeriod": "2025-05-31", "SummarizeColumnsBy": "Month", "Currency": "AED", "Option": [{"Name": "AccountingStandard", "Value": "PE"}, {"Name": "NoReportData", "Value": "false"}]}, "Columns": {"Column": [{"ColTitle": "", "ColType": "Account", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "account"}]}, {"ColTitle": "May 2025", "ColType": "Money", "MetaData": [{"Name": "StartDate", "Value": "2025-05-01"}, {"Name": "EndDate", "Value": "2025-05-31"}, {"Name": "<PERSON><PERSON><PERSON>", "Value": "May 2025"}]}, {"ColTitle": "Total", "ColType": "Money", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "total"}]}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Income"}, {"value": ""}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Sales of Product Income", "id": "82"}, {"value": "730355.39"}, {"value": "730355.39"}], "type": "Data"}, {"ColData": [{"value": "Discounts given", "id": "133"}, {"value": "-866.38"}, {"value": "-866.38"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Income"}, {"value": "729489.01"}, {"value": "729489.01"}]}, "type": "Section", "group": "Income"}, {"Header": {"ColData": [{"value": "Cost of Sales"}, {"value": ""}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Salaries COS", "id": "43"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Production Staff", "id": "269"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "<PERSON>", "id": "120"}, {"value": "10000.00"}, {"value": "10000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "181"}, {"value": "7000.00"}, {"value": "7000.00"}], "type": "Data"}, {"ColData": [{"value": "Rizad", "id": "147"}, {"value": "6650.00"}, {"value": "6650.00"}], "type": "Data"}, {"ColData": [{"value": "Nar", "id": "228"}, {"value": "6500.00"}, {"value": "6500.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "217"}, {"value": "4350.00"}, {"value": "4350.00"}], "type": "Data"}, {"ColData": [{"value": "Bharat", "id": "331"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "Bibek Nepal", "id": "319"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "Chaitanya Bapurao", "id": "1150040008"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "335"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "346"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "227"}, {"value": "3675.00"}, {"value": "3675.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON>", "id": "284"}, {"value": "3500.00"}, {"value": "3500.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "330"}, {"value": "3500.00"}, {"value": "3500.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "312"}, {"value": "3500.00"}, {"value": "3500.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "229"}, {"value": "3150.00"}, {"value": "3150.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "347"}, {"value": "3000.00"}, {"value": "3000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON>", "id": "345"}, {"value": "3000.00"}, {"value": "3000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "310"}, {"value": "3000.00"}, {"value": "3000.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Production Staff"}, {"value": "80825.00"}, {"value": "80825.00"}]}, "type": "Section"}]}, "Summary": {"ColData": [{"value": "Total Salaries COS"}, {"value": "80825.00"}, {"value": "80825.00"}]}, "type": "Section"}, {"ColData": [{"value": "Shippment Clearing Expenses", "id": "6"}, {"value": "3719.56"}, {"value": "3719.56"}], "type": "Data"}, {"ColData": [{"value": "Cost of sales", "id": "83"}, {"value": "228.57"}, {"value": "228.57"}], "type": "Data"}, {"ColData": [{"value": "Inventory", "id": "64"}, {"value": "0.00"}, {"value": "0.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Cost of Sales"}, {"value": "84773.13"}, {"value": "84773.13"}]}, "type": "Section", "group": "COGS"}, {"Summary": {"ColData": [{"value": "Gross Profit"}, {"value": "644715.88"}, {"value": "644715.88"}]}, "type": "Section", "group": "GrossProfit"}, {"Header": {"ColData": [{"value": "Other Income"}, {"value": ""}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Intrest Income", "id": "153"}, {"value": "2.03"}, {"value": "2.03"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Other Income"}, {"value": "2.03"}, {"value": "2.03"}]}, "type": "Section", "group": "OtherIncome"}, {"Header": {"ColData": [{"value": "Expenses"}, {"value": ""}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Other selling expenses", "id": "22"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "Distribution Charges", "id": "165"}, {"value": "15858.17"}, {"value": "15858.17"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "136"}, {"value": "7000.00"}, {"value": "7000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "282"}, {"value": "5148.39"}, {"value": "5148.39"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "197"}, {"value": "4450.00"}, {"value": "4450.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "231"}, {"value": "4250.00"}, {"value": "4250.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "177"}, {"value": "4240.00"}, {"value": "4240.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "311"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "id": "348"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>veed <PERSON>", "id": "336"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "338"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON>", "id": "301"}, {"value": "4000.00"}, {"value": "4000.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Other selling expenses"}, {"value": "60946.56"}, {"value": "60946.56"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Marketing Expenses", "id": "240"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "Facebook Ads", "id": "190"}, {"value": "14186.66"}, {"value": "14186.66"}], "type": "Data"}, {"ColData": [{"value": "Social Media - <PERSON><PERSON><PERSON>", "id": "273"}, {"value": "6000.00"}, {"value": "6000.00"}], "type": "Data"}, {"ColData": [{"value": "Onfleet", "id": "296"}, {"value": "5922.13"}, {"value": "5922.13"}], "type": "Data"}, {"ColData": [{"value": "Google Ads", "id": "277"}, {"value": "5466.08"}, {"value": "5466.08"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Marketing Expenses"}, {"value": "31574.87"}, {"value": "31574.87"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Salaries", "id": "71"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "<PERSON><PERSON>", "id": "42"}, {"value": "30000.00"}, {"value": "30000.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Salaries"}, {"value": "30000.00"}, {"value": "30000.00"}]}, "type": "Section"}, {"ColData": [{"value": "Vehicle Fuel-51035", "id": "146"}, {"value": "21017.01"}, {"value": "21017.01"}], "type": "Data"}, {"ColData": [{"value": "Commissions and fees", "id": "27"}, {"value": "12762.57"}, {"value": "12762.57"}], "type": "Data"}, {"ColData": [{"value": "STRIPE charges", "id": "294"}, {"value": "9011.23"}, {"value": "9011.23"}], "type": "Data"}, {"ColData": [{"value": "Shopify", "id": "157"}, {"value": "3350.97"}, {"value": "3350.97"}], "type": "Data"}, {"ColData": [{"value": "Equipment Expenses", "id": "173"}, {"value": "2864.24"}, {"value": "2864.24"}], "type": "Data"}, {"ColData": [{"value": "Legal and professional fees", "id": "52"}, {"value": "2189.71"}, {"value": "2189.71"}], "type": "Data"}, {"ColData": [{"value": "Dues and subscriptions", "id": "49"}, {"value": "1902.16"}, {"value": "1902.16"}], "type": "Data"}, {"ColData": [{"value": "Spinneys E-Commerce", "id": "239"}, {"value": "1542.70"}, {"value": "1542.70"}], "type": "Data"}, {"ColData": [{"value": "SSIP Portal Charges", "id": "234"}, {"value": "1542.70"}, {"value": "1542.70"}], "type": "Data"}, {"ColData": [{"value": "Qashio Transaction charges/ Subscription", "id": "351"}, {"value": "1406.43"}, {"value": "1406.43"}], "type": "Data"}, {"ColData": [{"value": "Repairs and Maintenance", "id": "57"}, {"value": "1082.80"}, {"value": "1082.80"}], "type": "Data"}, {"ColData": [{"value": "Cleaning Stuff", "id": "201"}, {"value": "1081.46"}, {"value": "1081.46"}], "type": "Data"}, {"ColData": [{"value": "Internet & Telephone Expenses", "id": "113"}, {"value": "900.00"}, {"value": "900.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON>", "id": "**********"}, {"value": "529.47"}, {"value": "529.47"}], "type": "Data"}, {"Header": {"ColData": [{"value": "Office expenses", "id": "54"}, {"value": ""}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "<PERSON><PERSON><PERSON>ses", "id": "145"}, {"value": "426.00"}, {"value": "426.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Office expenses"}, {"value": "426.00"}, {"value": "426.00"}]}, "type": "Section"}, {"ColData": [{"value": "Bank charges", "id": "48"}, {"value": "422.38"}, {"value": "422.38"}], "type": "Data"}, {"ColData": [{"value": "ZAPIER", "id": "298"}, {"value": "392.20"}, {"value": "392.20"}], "type": "Data"}, {"ColData": [{"value": "Google Cloud - GSuite", "id": "135"}, {"value": "298.62"}, {"value": "298.62"}], "type": "Data"}, {"ColData": [{"value": "Stationery and printing", "id": "58"}, {"value": "275.66"}, {"value": "275.66"}], "type": "Data"}, {"ColData": [{"value": "Missive", "id": "333"}, {"value": "272.30"}, {"value": "272.30"}], "type": "Data"}, {"ColData": [{"value": "Al Ansari Exchange Charges", "id": "106"}, {"value": "160.00"}, {"value": "160.00"}], "type": "Data"}, {"ColData": [{"value": "Quickbooks Fee", "id": "89"}, {"value": "151.20"}, {"value": "151.20"}], "type": "Data"}, {"ColData": [{"value": "Staff Uniform", "id": "196"}, {"value": "122.62"}, {"value": "122.62"}], "type": "Data"}, {"ColData": [{"value": "Digital Ocean", "id": "216"}, {"value": "113.80"}, {"value": "113.80"}], "type": "Data"}, {"ColData": [{"value": "Rawteen Commissions", "id": "344"}, {"value": "77.00"}, {"value": "77.00"}], "type": "Data"}, {"ColData": [{"value": "<PERSON><PERSON><PERSON>", "id": "320"}, {"value": "75.45"}, {"value": "75.45"}], "type": "Data"}, {"ColData": [{"value": "Bad debts", "id": "246"}, {"value": "74.42"}, {"value": "74.42"}], "type": "Data"}, {"ColData": [{"value": "Traning Expenses", "id": "116"}, {"value": "70.00"}, {"value": "70.00"}], "type": "Data"}, {"ColData": [{"value": "Parking Expenses", "id": "156"}, {"value": "28.57"}, {"value": "28.57"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Expenses"}, {"value": "186665.10"}, {"value": "186665.10"}]}, "type": "Section", "group": "Expenses"}, {"Header": {"ColData": [{"value": "Other Expenses"}, {"value": ""}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Pemo Charges", "id": "305"}, {"value": "232.00"}, {"value": "232.00"}], "type": "Data"}, {"ColData": [{"value": "Other Expense", "id": "101"}, {"value": "58.00"}, {"value": "58.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Other Expenses"}, {"value": "290.00"}, {"value": "290.00"}]}, "type": "Section", "group": "OtherExpenses"}, {"Summary": {"ColData": [{"value": "Net earnings"}, {"value": "457762.81"}, {"value": "457762.81"}]}, "type": "Section", "group": "Netearnings"}]}}