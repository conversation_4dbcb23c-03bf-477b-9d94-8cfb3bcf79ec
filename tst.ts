import ARIMA from "arima";

/**
 * Interface for a single metadata entry within a column.
 */
interface ColumnMetaData {
  Name: string;
  Value: string;
}

/**
 * Interface for a column definition in the report.
 */
interface ReportColumn {
  ColTitle: string;
  ColType: string;
  MetaData?: ColumnMetaData[];
}

/**
 * Interface for a single data cell within a row.
 */
interface ColData {
  value: string;
  id?: string;
}

/**
 * Interface for the header section of a row.
 */
interface RowHeader {
  ColData: ColData[];
}

/**
 * Interface for the summary section of a row.
 */
interface RowSummary {
  ColData: ColData[];
}

/**
 * Recursive interface for report rows, which can be sections or data rows.
 */
interface ReportRow {
  Header?: RowHeader; // Present for "Section" rows
  Rows?: {
    Row: ReportRow[]; // Nested rows for sections
  };
  ColData?: ColData[]; // Present for "Data" rows
  Summary?: RowSummary; // Present for "Section" summaries or top-level summaries (like Net earnings)
  type: "Section" | "Data"; // Type of the row
  group?: string; // Group name for sections (e.g., "Income", "COGS")
}

/**
 * Interface for the full input report data.
 */
interface ReportData {
  Header: {
    Time: string;
    ReportName: string;
    DateMacro: string;
    ReportBasis: string;
    StartPeriod: string;
    EndPeriod: string;
    SummarizeColumnsBy: string;
    Currency: string;
    Option: Array<{ Name: string; Value: string }>;
  };
  Columns: {
    Column: ReportColumn[];
  };
  Rows: {
    Row: ReportRow[];
  };
}

/**
 * Interface for a single monthly data point in the output.
 */
interface MonthlyDataPoint {
  date: Date;
  amount: number;
}

/**
 * Interface for a single class data point in the output.
 */
interface ClassDataPoint {
  className: string;
  amount: number;
}

/**
 * Interface for the transformed account format.
 */
interface TransformedAccount {
  account_name: string;
  start_date: Date | null; // Date of the first non-zero monthly value
  total_amount: number;
  total_monthly: MonthlyDataPoint[]; // Array of {date, amount} objects, filtered for leading zeros
  avg_monthly: number; // Average of the last 12 months from total_monthly
  account_type: "Income" | "Other Income" | null; // Only "Income" or "Other Income"
  next_month_prediction: {
    amount: number;
    diff: number; // Standard deviation of the prediction
  } | null;
  classes: ClassDataPoint[] | null; // New field for class breakdown
}

/**
 * Interface for the final desired output format.
 */
export interface RevenueStreamsReportI {
  report_date: Date;
  Currency: string;
  accounts: TransformedAccount[];
  total_income: number;
  avg_monthly_total_income: number;
  expected_total_income_next_month: { amount: number; diff: number } | null;
}

/**
 * Transforms a financial report data structure into a comprehensive financial overview.
 * Extracts account details, monthly trends, and class breakdowns for Income and Other Income.
 * Also calculates overall total income, its average monthly value, and a next-month prediction.
 *
 * @param reportData The input report data for monthly breakdown.
 * @param classesReportData Optional report data containing class breakdown for accounts.
 * @returns A comprehensive financial overview object.
 */
function transformFinancialReport(
  reportData: ReportData,
  classesReportData: ReportData | null = null,
): RevenueStreamsReportI {
  const transformedAccounts: TransformedAccount[] = [];

  const columns = reportData.Columns.Column;
  // Store detailed info for each monthly column to map index to date
  const monthlyColumnDetails: Array<{ index: number; date: Date }> = [];
  let totalColumnIndex: number = -1;

  // 1. Pre-process columns from the main reportData to find indices and dates for monthly data and total.
  columns.forEach((col, index) => {
    const colKeyMeta = col.MetaData?.find((m) => m.Name === "ColKey");
    if (colKeyMeta) {
      if (colKeyMeta.Value === "total") {
        totalColumnIndex = index;
      } else if (
        col.ColType === "Money" &&
        colKeyMeta.Value.match(
          /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s\d{4}$/,
        )
      ) {
        // Only consider columns that look like "Month Year" for monthly data
        const startDateMeta = col.MetaData?.find((m) => m.Name === "StartDate");
        if (startDateMeta) {
          monthlyColumnDetails.push({
            index: index,
            date: new Date(startDateMeta.Value),
          });
        }
      }
    }
  });

  // Ensure monthly columns are sorted by date, as data may not always be perfectly ordered.
  monthlyColumnDetails.sort((a, b) => a.date.getTime() - b.date.getTime());

  // 2. Pre-process classesReportData if provided
  const classesDataByAccount: Map<string, ClassDataPoint[]> = new Map();
  if (classesReportData) {
    const classColumnDetails: Array<{ index: number; className: string }> = [];
    classesReportData.Columns.Column.forEach((col, index) => {
      // Skip the first column (account name) and the "total" column
      if (index === 0 || !col.MetaData?.[0]?.Value) return;

      const colKeyMeta = col.MetaData?.find((m) => m.Name === "ColKey");
      if (col.ColType === "Money" && colKeyMeta?.Value !== "total") {
        classColumnDetails.push({
          index: index,
          // Use ColTitle as className for readability, assuming it's the class name
          className: col.ColTitle,
        });
      }
    });

    // Recursively process classesReportData rows to populate classesDataByAccount
    const processClassesRowsRecursively = (rows: ReportRow[]) => {
      for (const row of rows) {
        if (row.type === "Section") {
          if (row.Rows && row.Rows.Row) {
            processClassesRowsRecursively(row.Rows.Row); // Recurse for nested data
          }
          // Process summary rows of sections
          // if (row.Summary && row.Summary.ColData?.[0]?.value) {
          //   const accountName = row.Summary.ColData[0].value;
          //   const accountClasses: ClassDataPoint[] = [];
          //   for (const classDetail of classColumnDetails) {
          //     const valueStr = row.Summary.ColData[classDetail.index]?.value;
          //     const amount = parseFloat(valueStr || "0");
          //     if (amount !== 0) {
          //       // Only include non-zero amounts
          //       accountClasses.push({
          //         className: classDetail.className,
          //         amount: amount,
          //       });
          //     }
          //   }
          //   if (accountClasses.length > 0) {
          //     classesDataByAccount.set(accountName, accountClasses);
          //   }
          // }
        } else if (row.type === "Data" && row.ColData?.[0]?.value) {
          const accountName = row?.ColData?.[0]?.value;
          const accountClasses: ClassDataPoint[] = [];
          for (const classDetail of classColumnDetails) {
            const valueStr = row.ColData[classDetail.index]?.value;
            const amount = parseFloat(valueStr || "0");
            if (amount !== 0) {
              // Only include non-zero amounts
              accountClasses.push({
                className: classDetail.className,
                amount: amount,
              });
            }
          }
          if (accountClasses.length > 0) {
            classesDataByAccount.set(accountName, accountClasses);
          }
        }
      }
    };
    processClassesRowsRecursively(classesReportData.Rows.Row);
  }

  /**
   * Calculates the average of the last 12 monthly values from the filtered monthly data.
   * @param monthlyData An array of monthly data points ({ date: Date, amount: number }).
   * @returns The average of the last 12 months, or 0 if no data.
   */
  const calculateAvgMonthly = (monthlyData: MonthlyDataPoint[]): number => {
    if (monthlyData.length === 0) return 0;
    // Get the last 12 months, or fewer if the array is smaller than 12.
    const relevantMonths = monthlyData.slice(
      Math.max(0, monthlyData.length - 12),
    );

    const sum = relevantMonths.reduce((acc, entry) => acc + entry.amount, 0);
    return sum / relevantMonths.length;
  };

  /**
   * Predicts the next month's amount using ARIMA model.
   * Requires at least 36 data points for meaningful seasonal prediction.
   * @param monthlyData An array of monthly data points.
   * @returns An object with predicted amount and standard deviation, or null if prediction is not possible.
   */
  const predictNextMonth = (
    monthlyData: MonthlyDataPoint[],
  ): {
    amount: number;
    diff: number; // Standard deviation
  } | null => {
    const cleanData = monthlyData
      .map((entry) => entry.amount)
      .filter((v) => typeof v === "number" && !isNaN(v));

    if (cleanData.length < 36) {
      // ARIMA with seasonality (s=12) needs enough data, 36 is a common heuristic
      return null;
    }

    try {
      // Use autoARIMA to find best p,d,q,P,D,Q,s parameters automatically
      const arima = new ARIMA({
        auto: true, // Enable AutoARIMA
        // Max orders to search
        p: 2,
        d: 1,
        q: 2,
        P: 1,
        D: 1,
        Q: 1,
        s: 12, // Monthly data = yearly seasonality
        approximation: 1, // Use approximation for speed
        search: 1, // Exhaustive search (more accurate)
        method: 0, // CSS estimation
        optimizer: 6, // TNC optimizer
        verbose: false, // Suppress verbose output from arima library
      }).train(cleanData);

      const predictionResult = arima.predict(1); // Predict 1 step ahead

      if (
        Array.isArray(predictionResult) &&
        predictionResult.length === 2 &&
        typeof predictionResult[0]?.[0] === "number" &&
        typeof predictionResult[1]?.[0] === "number"
      ) {
        const predictedAmount = predictionResult[0][0];
        const variance = predictionResult[1][0];
        const standardDeviation = Math.sqrt(Math.max(0, variance)); // Ensure non-negative for sqrt
        return {
          amount: predictedAmount,
          diff: standardDeviation,
        };
      }
    } catch (e) {
      // console.error("ARIMA prediction failed:", e); // Log error for debugging if needed
    }
    return null;
  };

  /**
   * Processes a single data row or a section summary row to extract account information.
   * Filters out leading zero months and calculates the start_date accordingly.
   * @param colData The ColData array from either a Row's ColData or a Summary's ColData.
   * @param accountType The determined account type ("Income" or "Other Income").
   */
  const processAccountRow = (
    colData: ColData[],
    accountType: "Income" | "Other Income",
  ) => {
    const account_name = colData[0]?.value;
    if (!account_name || totalColumnIndex === -1) {
      return; // Skip if account name or total column is missing
    }

    const total_amount = parseFloat(colData[totalColumnIndex]?.value || "0");

    const fullMonthlyValues: MonthlyDataPoint[] = [];
    for (const monthDetail of monthlyColumnDetails) {
      const amount = parseFloat(colData[monthDetail.index]?.value || "0");
      fullMonthlyValues.push({ date: monthDetail.date, amount: amount });
    }

    // Filter leading zero months: only include values from the first non-zero month onwards.
    let foundNonZero = false;
    const filteredMonthlyValues: MonthlyDataPoint[] = [];
    for (const month of fullMonthlyValues) {
      if (foundNonZero || month.amount !== 0) {
        filteredMonthlyValues.push(month);
        foundNonZero = true;
      }
    }

    // Set start_date to the date of the first non-zero entry, or null if all are zero.
    const actual_start_date =
      filteredMonthlyValues.length > 0 ? filteredMonthlyValues[0]?.date : null;

    // Retrieve class data for this account
    const accountClasses = classesDataByAccount.get(account_name) || null;

    transformedAccounts.push({
      account_name: account_name,
      start_date: actual_start_date || null,
      total_amount: total_amount,
      total_monthly: filteredMonthlyValues,
      avg_monthly: calculateAvgMonthly(filteredMonthlyValues),
      next_month_prediction: null, //predictNextMonth(filteredMonthlyValues),
      account_type: accountType,
      classes: accountClasses, // Assign the retrieved classes
    });
  };

  /**
   * Recursively processes report rows.
   * @param rows The array of rows to process.
   * @param inheritedAccountType The account type inherited from the parent section.
   *                             This dictates which 'Data' rows should be processed.
   */
  const processRowsRecursively = (
    rows: ReportRow[],
    inheritedAccountType: "Income" | "Other Income" | null,
  ) => {
    for (const row of rows) {
      if (row.type === "Section") {
        let currentSectionAccountType: "Income" | "Other Income" | null = null;
        // Check if this section itself represents an "Income" or "Other Income" group.
        if (row.group === "Income") {
          currentSectionAccountType = "Income";
        } else if (row.group === "OtherIncome") {
          currentSectionAccountType = "Other Income";
        }

        // Recursively process any nested rows, passing down the most specific account type.
        // If the current section is an income type, its children inherit that type.
        // Otherwise, children inherit the previously inherited type (or null if none).
        const typeToPassDown =
          currentSectionAccountType || inheritedAccountType;
        if (row.Rows && row.Rows.Row) {
          processRowsRecursively(row.Rows.Row, typeToPassDown);
        }

        // If the current section is an "Income" or "Other Income" section,
        // process its summary row if it exists.
        // if (
        //   row.Summary &&
        //   (currentSectionAccountType === "Income" ||
        //     currentSectionAccountType === "Other Income")
        // ) {
        //   processAccountRow(row.Summary.ColData, currentSectionAccountType);
        // }
      } else if (row.type === "Data") {
        // Only process data rows if they are under a recognized "Income" or "Other Income" section.
        if (
          inheritedAccountType === "Income" ||
          inheritedAccountType === "Other Income"
        ) {
          processAccountRow(row?.ColData || [], inheritedAccountType);
        }
      }
    }
  };

  // Start the recursive processing from the top-level rows with no initially inherited account type.
  processRowsRecursively(reportData.Rows.Row, null);

  // 3. Calculate overall income metrics
  const incomeAccounts = transformedAccounts.filter(
    (acc) =>
      acc.account_type === "Income" || acc.account_type === "Other Income",
  );

  let totalIncomeAmount = 0;
  const monthlyIncomeMap = new Map<string, number>(); // Key: 'YYYY-MM', Value: total amount

  for (const account of incomeAccounts) {
    totalIncomeAmount += account.total_amount;
    for (const monthlyPoint of account.total_monthly) {
      // Use a consistent key for each month (e.g., 'YYYY-MM')
      const monthKey = `${monthlyPoint.date.getFullYear()}-${(
        monthlyPoint.date.getMonth() + 1
      )
        .toString()
        .padStart(2, "0")}`;
      monthlyIncomeMap.set(
        monthKey,
        (monthlyIncomeMap.get(monthKey) || 0) + monthlyPoint.amount,
      );
    }
  }

  // Convert the map back to a sorted array of MonthlyDataPoint
  const allIncomeMonthlyData: MonthlyDataPoint[] = Array.from(
    monthlyIncomeMap.entries(),
  )
    .map(([key, amount]) => ({
      // Reconstruct date, assuming start of the month for consistency
      date: new Date(`${key}-01`),
      amount: amount,
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  const avgMonthlyTotalIncome = calculateAvgMonthly(allIncomeMonthlyData);
  const predictedTotalIncomeNextMonth = null; //predictNextMonth(allIncomeMonthlyData);

  return {
    report_date: new Date(reportData.Header.Time),
    Currency: reportData.Header.Currency,
    accounts: transformedAccounts,
    total_income: totalIncomeAmount,
    avg_monthly_total_income: avgMonthlyTotalIncome,
    expected_total_income_next_month: predictedTotalIncomeNextMonth,
  };
}
export default transformFinancialReport;
