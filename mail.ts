import nodemailer from "nodemailer";

async function main() {
  let transporter = nodemailer.createTransport({
    host: "SMTP.titan.email",
    port: 465,
    secure: false,
    logger: true,
    auth: {
      user: "<EMAIL>",
      pass: "Wilfredo@2025!#",
      //   type: "LOGIN",
    },
  });
  console.log(await transporter);

  let info = await transporter.sendMail({
    from: "Development <<EMAIL>>",
    to: "<EMAIL>",
    subject: "Hello",
    text: "Hello world?",
    html: "<b>Hello world?</b>",
  });

  console.log("Message sent: %s", info);
}

main();
