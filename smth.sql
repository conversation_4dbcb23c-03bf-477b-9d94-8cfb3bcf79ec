SELECT
    "PROJECT",
    "CUSTOME<PERSON>",
    "DESC<PERSON>P<PERSON><PERSON>",
    "C<PERSON><PERSON><PERSON>",
    "projStatus",
    "C_DELIVERED_BY",
    location,
    "externalProj",
    date_received,
    "projDateRecd",
    "DATE_STARTED",
    "DATE_COMPLETED",
    "DATE_REVIEWED",
    "SAMPLE_NUMBER",
    "TEXT_ID",
    "Source",
    "ORIGINAL_SAMPLE",
    "SAMPLED_DATE",
    "sampleTemplate",
    "ANALYSIS",
    "TEST_NUMBER",
    "RESULT_NUMBER",
    "Lab",
    "rawResult",
    "FORMATTED_ENTRY",
    "SampleStatus",
    "TestStatus",
    "FinalResult_Value",
    "Population",
    "Analyte_Group",
    reported_name,
    name,
    "Analyte",
    "labName",
    "sampleType",
    "resChangedOn",
    "resReviwedDate"
FROM
    data.datav1;