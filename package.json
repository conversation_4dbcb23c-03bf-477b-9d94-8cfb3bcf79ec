{"name": "t3tempo", "version": "0.1.0", "private": true, "type": "module", "scripts": {"check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "dev:turbo": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "test -d prisma && prisma generate || echo 'Skipping prisma generate';", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "build": "next build", "start": "next start", "start:pm2": "pm2 start npm --name 'wilfred<PERSON>' -- start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.797.0", "@google/genai": "^1.4.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@react-spring/web": "^10.0.1", "@shopify/shopify-api": "^11.12.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/similarity": "^1.2.3", "arima": "^0.2.5", "bcryptjs": "^3.0.2", "check-password-strength": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "intuit-oauth": "^4.2.0", "lucide-react": "^0.487.0", "motion": "^12.6.5", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.10.1", "qbo": "^0.3.2", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "server-only": "^0.0.1", "similarity": "^1.2.1", "superjson": "^2.2.1", "tailwind-merge": "^3.2.0", "text-similarity-scorer": "^0.0.10", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "babel-plugin-react-compiler": "^19.0.0-beta-e993439-20250405", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "ignore-loader": "^0.1.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@11.2.0"}