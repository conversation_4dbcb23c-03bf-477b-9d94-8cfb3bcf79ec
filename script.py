def classify_quickbooks_accounts(chart_of_accounts, trial_balance_data):
    """
    Classifies QuickBooks accounts for cash flow forecasting.
    
    Args:
        chart_of_accounts: List of account dictionaries with keys:
            - account_id, account_name, account_type, account_sub_type, 
            - description, current_balance, active_status
        trial_balance_data: List of activity dictionaries with keys:
            - account_name, total_debits_12m, total_credits_12m
    
    Returns:
        Dictionary with summary stats and classified accounts
    """
    
    # Step 1: Filter relevant accounts
    filtered_accounts = filter_relevant_accounts(chart_of_accounts, trial_balance_data)
    
    # Step 2: Classify each account
    classified_accounts = []
    for account in filtered_accounts:
        classification = classify_single_account(account)
        classified_accounts.append(classification)
    
    # Step 3: Generate summary
    summary = generate_summary(chart_of_accounts, classified_accounts)
    
    return {
        'summary': summary,
        'classified_accounts': classified_accounts
    }


def filter_relevant_accounts(chart_of_accounts, trial_balance_data):
    """Filter accounts based on activity and materiality thresholds."""
    
    # Create activity lookup dictionary
    activity_lookup = {item['account_name']: item for item in trial_balance_data}
    [1,2,3,4]
    filtered_accounts = []
    
    for account in chart_of_accounts:
        account_name = account['Id']
        current_balance = abs(float(account.get('current_balance', 0)))
        
        # Get activity data
        activity = activity_lookup.get(account_name, {})
        total_debits = float(activity.get('total_debits_12m', 0))
        total_credits = float(activity.get('total_credits_12m', 0))
        total_activity = total_debits + total_credits
        
        # Apply filtering criteria
        include_account = False
        
        # Criterion 1: Significant activity
        if total_activity >= 500:
            include_account = True
       
        
        # Criterion 2: Material current balance
        elif current_balance >= 100:
            include_account = True
        
        # Criterion 3: Inherently cash-related accounts
        elif is_inherently_cash_related(account):
            include_account = True
        
        
        # Exclude non-cash accounting entries
        if is_non_cash_account(account):
            include_account = False
        
        # Exclude equity accounts
        if account.get('account_type', '').lower() == 'equity':
            include_account = False
        
        # value -> true
        if include_account:
            # Add activity data to account record
            account_with_activity = account.copy()
            account_with_activity['total_debits_12m'] = total_debits
            account_with_activity['total_credits_12m'] = total_credits
            filtered_accounts.append(account_with_activity)
    
    return filtered_accounts


def is_inherently_cash_related(account):
    """Check if account is inherently cash-related regardless of activity."""
    account_type = account.get('account_type', '').lower()
    account_sub_type = account.get('account_sub_type', '').lower()
    account_name = account.get('account_name', '').lower()
    
    # Bank and cash accounts
    cash_keywords = ['bank', 'cash', 'checking', 'savings', 'petty cash', 'money market']
    if any(keyword in account_name for keyword in cash_keywords):
        return True
    
    # Credit cards and lines of credit
    credit_keywords = ['credit card', 'line of credit', 'visa', 'mastercard', 'amex']
    if any(keyword in account_name for keyword in credit_keywords):
        return True
    
    # Accounts receivable and payable
    if 'receivable' in account_sub_type or 'payable' in account_sub_type:
        return True
    
    # Loans
    if 'loan' in account_sub_type or 'note' in account_sub_type:
        return True
    
    return False


def is_non_cash_account(account):
    """Check if account represents non-cash accounting entries."""
    account_name = account.get('account_name', '').lower()
    account_sub_type = account.get('account_sub_type', '').lower()
    
    non_cash_keywords = [
        'depreciation', 'amortization', 'accumulated depreciation',
        'unrealized', 'contra', 'allowance for doubtful'
    ]
    
    return any(keyword in account_name or keyword in account_sub_type 
              for keyword in non_cash_keywords)


def classify_single_account(account):
    """Classify a single account into cash flow categories."""
    account_type = account.get('account_type', '').lower()
    account_sub_type = account.get('account_sub_type', '').lower()
    account_name = account.get('account_name', '').lower()
    
    classification = {
        'account_name': account['account_name'],
        'account_type': account.get('account_type', ''),
        'account_sub_type': account.get('account_sub_type', ''),
        'debits_12m': account.get('total_debits_12m', 0),
        'credits_12m': account.get('total_credits_12m', 0),
        'current_balance': account.get('current_balance', 0),
        'cash_flow_category': '',
        'confidence': 'High',
        'notes': ''
    }
    
    # Classify based on account type and characteristics
    if account_type == 'income':
        classification['cash_flow_category'] = 'Current Cash Inflow'
        
    elif account_type == 'expense':
        classification['cash_flow_category'] = 'Current Cash Outflow'
        
    elif 'receivable' in account_sub_type:
        classification['cash_flow_category'] = 'Future Cash Inflow'
        
    elif 'payable' in account_sub_type:
        classification['cash_flow_category'] = 'Future Cash Outflow'
        
    elif account_type == 'asset':
        if any(keyword in account_name for keyword in ['bank', 'cash', 'checking', 'savings']):
            classification['cash_flow_category'] = 'Cash Position'
        elif any(keyword in account_name for keyword in ['inventory', 'stock']):
            classification['cash_flow_category'] = 'Working Capital'
        elif any(keyword in account_name for keyword in ['prepaid', 'deposits']):
            classification['cash_flow_category'] = 'Working Capital'
        else:
            classification['cash_flow_category'] = 'Non-Cash/Excluded'
            classification['confidence'] = 'Medium'
            classification['notes'] = 'Asset account - verify cash impact'
            
    elif account_type == 'liability':
        if any(keyword in account_name for keyword in ['credit card', 'line of credit']):
            classification['cash_flow_category'] = 'Cash Position'
        elif any(keyword in account_name for keyword in ['loan', 'note payable']):
            classification['cash_flow_category'] = 'Future Cash Outflow'
        elif any(keyword in account_name for keyword in ['accrued', 'payroll', 'tax']):
            classification['cash_flow_category'] = 'Future Cash Outflow'
        elif any(keyword in account_name for keyword in ['deferred', 'unearned']):
            classification['cash_flow_category'] = 'Working Capital'
        else:
            classification['cash_flow_category'] = 'Future Cash Outflow'
            classification['confidence'] = 'Medium'
            classification['notes'] = 'Liability account - assumed future outflow'
    
    else:
        classification['cash_flow_category'] = 'Non-Cash/Excluded'
        classification['confidence'] = 'Low'
        classification['notes'] = f'Unusual account type: {account_type}'
    
    # Flag unusual activity patterns
    debits = float(classification['debits_12m'])
    credits = float(classification['credits_12m'])
    
    if debits > 0 and credits > 0 and abs(debits - credits) / max(debits, credits) < 0.1:
        classification['notes'] += ' High bidirectional activity'
        if classification['confidence'] == 'High':
            classification['confidence'] = 'Medium'
    
    return classification


def generate_summary(original_accounts, classified_accounts):
    """Generate summary statistics."""
    total_analyzed = len(original_accounts)
    total_included = len(classified_accounts)
    total_excluded = total_analyzed - total_included
    
    # Count by category
    category_counts = {}
    for account in classified_accounts:
        category = account['cash_flow_category']
        category_counts[category] = category_counts.get(category, 0) + 1
    
    return {
        'total_accounts_analyzed': total_analyzed,
        'accounts_included_for_cash_flow': total_included,
        'accounts_excluded': total_excluded,
        'category_breakdown': category_counts,
        'data_quality_issues': []  # Could add validation checks here
    }


# Example usage:
if __name__ == "__main__":
    # Sample data structure
    sample_chart_of_accounts = [
        {
            'account_id': '1',
            'account_name': 'Checking Account',
            'account_type': 'Asset',
            'account_sub_type': 'Bank',
            'description': 'Main business checking',
            'current_balance': 15000,
            'active_status': True
        },
        {
            'account_id': '2',
            'account_name': 'Sales Revenue',
            'account_type': 'Income',
            'account_sub_type': 'Sales of Product Income',
            'description': 'Product sales',
            'current_balance': 0,
            'active_status': True
        }
    ]
    
    sample_trial_balance = [
        {
            'account_name': 'Checking Account',
            'total_debits_12m': 250000,
            'total_credits_12m': 230000
        },
        {
            'account_name': 'Sales Revenue',
            'total_debits_12m': 0,
            'total_credits_12m': 180000
        }
    ]
    
    result = classify_quickbooks_accounts(sample_chart_of_accounts, sample_trial_balance)
    
    print("Summary:", result['summary'])
    print("\nClassified Accounts:")
    for account in result['classified_accounts']:
        print(f"  {account['account_name']}: {account['cash_flow_category']}")